#!/usr/bin/env python3
"""
演示数据生成脚本
生成一些示例安全事件和系统指标数据用于演示
"""

import sys
import time
import random
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.storage.database import db_manager
from src.core.logger import app_logger


def generate_security_events():
    """生成示例安全事件"""
    print("生成示例安全事件...")
    
    # 示例攻击类型和源IP
    attack_types = [
        ('syn_flood', 'SYN洪水攻击', 'high'),
        ('port_scan', '端口扫描', 'medium'),
        ('sql_injection', 'SQL注入攻击', 'high'),
        ('dns_tunneling', 'DNS隧道', 'high'),
        ('brute_force', '暴力破解', 'medium'),
        ('malware_communication', '恶意软件通信', 'critical'),
        ('data_exfiltration', '数据泄露', 'critical'),
        ('ddos_attack', 'DDoS攻击', 'high')
    ]
    
    source_ips = [
        '***********00', '***********01', '*************',
        '*********', '*********', '************',
        '************', '*************', '************'
    ]
    
    target_ips = [
        '***********', '***********0', '*******',
        '*******', '***************', '*********'
    ]
    
    # 生成过去24小时的事件
    base_time = datetime.now() - timedelta(hours=24)
    
    for i in range(20):
        attack_type, title, severity = random.choice(attack_types)
        src_ip = random.choice(source_ips)
        dst_ip = random.choice(target_ips)
        
        # 随机时间
        event_time = base_time + timedelta(
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59),
            seconds=random.randint(0, 59)
        )
        
        event_data = {
            'event_id': f'event_{int(time.time())}_{i}',
            'event_type': 'security_alert',
            'severity': severity,
            'title': title,
            'description': f'检测到来自 {src_ip} 的{title}行为，目标: {dst_ip}',
            'src_ip': src_ip,
            'dst_ip': dst_ip,
            'src_port': random.randint(1024, 65535),
            'dst_port': random.choice([80, 443, 22, 21, 25, 53, 3389]),
            'protocol': random.choice(['TCP', 'UDP']),
            'attack_type': attack_type,
            'confidence': round(random.uniform(0.7, 1.0), 2),
            'impact_score': round(random.uniform(0.3, 1.0), 2),
            'first_seen': event_time.isoformat(),
            'last_seen': event_time.isoformat()
        }
        
        event_id = db_manager.save_security_event(event_data)
        if event_id:
            print(f"  ✅ 创建安全事件: {title} (ID: {event_id})")
        
        # 避免过快插入
        time.sleep(0.1)


def generate_system_metrics():
    """生成系统指标数据"""
    print("生成系统指标数据...")
    
    # 生成过去2小时的指标数据
    base_time = datetime.now() - timedelta(hours=2)
    
    for i in range(120):  # 每分钟一个数据点
        timestamp = base_time + timedelta(minutes=i)
        
        # 模拟流量数据
        packets_per_second = random.randint(50, 500)
        bytes_per_second = packets_per_second * random.randint(64, 1500)
        cpu_usage = random.uniform(10, 80)
        memory_usage = random.uniform(30, 70)
        
        # 保存指标
        db_manager.save_system_metric('packets_per_second', packets_per_second, 'pps')
        db_manager.save_system_metric('bytes_per_second', bytes_per_second, 'bps')
        db_manager.save_system_metric('cpu_usage', cpu_usage, '%')
        db_manager.save_system_metric('memory_usage', memory_usage, '%')
    
    print(f"  ✅ 生成了 {120 * 4} 个系统指标数据点")


def generate_packet_records():
    """生成数据包记录"""
    print("生成数据包记录...")
    
    protocols = ['TCP', 'UDP', 'ICMP']
    
    for i in range(50):
        packet_data = {
            'session_id': f'session_{int(time.time())}_{i}',
            'timestamp': datetime.now().isoformat(),
            'src_ip': f'192.168.1.{random.randint(100, 200)}',
            'dst_ip': f'8.8.8.{random.randint(1, 8)}',
            'src_port': random.randint(1024, 65535),
            'dst_port': random.choice([80, 443, 53, 22]),
            'protocol': random.choice(protocols),
            'packet_size': random.randint(64, 1500),
            'application_protocol': random.choice(['HTTP', 'HTTPS', 'DNS', 'SSH'])
        }
        
        record_id = db_manager.save_packet_record(packet_data)
        if record_id and i % 10 == 0:
            print(f"  ✅ 创建数据包记录: {i+1}/50")


def main():
    """主函数"""
    print("网络流量分析与安全检测系统 - 演示数据生成")
    print("=" * 60)
    
    try:
        # 生成安全事件
        generate_security_events()
        print()
        
        # 生成系统指标
        generate_system_metrics()
        print()
        
        # 生成数据包记录
        generate_packet_records()
        print()
        
        print("=" * 60)
        print("演示数据生成完成！")
        print()
        print("现在可以访问Web界面查看数据:")
        print("http://localhost:8080")
        print()
        print("数据统计:")
        
        # 显示统计信息
        events = db_manager.get_security_events(limit=100)
        print(f"- 安全事件: {len(events)} 条")
        
        # 按严重程度统计
        severity_count = {}
        for event in events:
            severity = event['severity']
            severity_count[severity] = severity_count.get(severity, 0) + 1
        
        for severity, count in severity_count.items():
            print(f"  - {severity}: {count} 条")
        
    except Exception as e:
        print(f"❌ 生成演示数据失败: {e}")
        app_logger.error(f"生成演示数据失败: {e}")


if __name__ == "__main__":
    main()
