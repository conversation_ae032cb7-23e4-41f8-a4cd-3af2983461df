"""
Web管理界面应用
提供流量监控、安全事件查看、系统管理等功能
"""

from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
from datetime import datetime, timedelta
import json

from ..core.config import config
from ..core.logger import app_logger
from ..storage.database import db_manager
from ..capture.packet_capture import packet_capture
from ..storage.pcap_storage import pcap_storage
from ..detection.rule_engine import rule_engine


def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    
    # 配置
    app.config['SECRET_KEY'] = config.get('web.secret_key', 'default-secret-key')
    app.config['JSON_AS_ASCII'] = False
    
    # 启用CORS
    CORS(app)
    
    # 注册路由
    register_routes(app)
    
    return app


def register_routes(app):
    """注册路由"""
    
    @app.route('/')
    def index():
        """主页"""
        return render_template('index.html')
    
    @app.route('/api/status')
    def api_status():
        """获取系统状态"""
        try:
            status = {
                'system': {
                    'name': config.get('system.name'),
                    'version': config.get('system.version'),
                    'uptime': _get_uptime(),
                    'timestamp': datetime.now().isoformat()
                },
                'capture': packet_capture.get_stats(),
                'storage': pcap_storage.get_stats(),
                'detection': rule_engine.get_rule_stats()
            }
            return jsonify(status)
        except Exception as e:
            app_logger.error(f"获取系统状态失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/security-events')
    def api_security_events():
        """获取安全事件列表"""
        try:
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 20, type=int)
            severity = request.args.get('severity')
            
            offset = (page - 1) * per_page
            events = db_manager.get_security_events(
                limit=per_page,
                offset=offset,
                severity=severity
            )
            
            return jsonify({
                'events': events,
                'page': page,
                'per_page': per_page,
                'total': len(events)
            })
        except Exception as e:
            app_logger.error(f"获取安全事件失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/traffic-stats')
    def api_traffic_stats():
        """获取流量统计"""
        try:
            # 获取时间范围
            hours = request.args.get('hours', 24, type=int)
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            # 这里应该从数据库查询统计数据
            # 为了演示，返回模拟数据
            stats = {
                'time_range': {
                    'start': start_time.isoformat(),
                    'end': end_time.isoformat()
                },
                'packet_count': packet_capture.get_stats().get('total_packets', 0),
                'protocol_distribution': {
                    'TCP': 60,
                    'UDP': 30,
                    'ICMP': 5,
                    'Other': 5
                },
                'top_sources': [
                    {'ip': '*************', 'packets': 1500},
                    {'ip': '*************', 'packets': 1200},
                    {'ip': '*************', 'packets': 800}
                ],
                'top_destinations': [
                    {'ip': '*******', 'packets': 2000},
                    {'ip': '*******', 'packets': 1500},
                    {'ip': '***************', 'packets': 1000}
                ]
            }
            
            return jsonify(stats)
        except Exception as e:
            app_logger.error(f"获取流量统计失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/pcap-files')
    def api_pcap_files():
        """获取PCAP文件列表"""
        try:
            files = pcap_storage.get_file_list()
            return jsonify({'files': files})
        except Exception as e:
            app_logger.error(f"获取PCAP文件列表失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/rules')
    def api_rules():
        """获取检测规则列表"""
        try:
            rules_data = []
            for rule_id, rule in rule_engine.rules.items():
                rule_info = {
                    'id': rule.id,
                    'name': rule.name,
                    'description': rule.description,
                    'severity': rule.severity,
                    'category': rule.category,
                    'enabled': rule.enabled,
                    'stats': rule_engine.rule_stats.get(rule_id, {})
                }
                rules_data.append(rule_info)
            
            return jsonify({'rules': rules_data})
        except Exception as e:
            app_logger.error(f"获取规则列表失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/rules/<rule_id>/toggle', methods=['POST'])
    def api_toggle_rule(rule_id):
        """切换规则启用状态"""
        try:
            if rule_id not in rule_engine.rules:
                return jsonify({'error': '规则不存在'}), 404
            
            rule = rule_engine.rules[rule_id]
            rule.enabled = not rule.enabled
            
            return jsonify({
                'rule_id': rule_id,
                'enabled': rule.enabled,
                'message': f"规则 {rule.name} 已{'启用' if rule.enabled else '禁用'}"
            })
        except Exception as e:
            app_logger.error(f"切换规则状态失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/capture/start', methods=['POST'])
    def api_start_capture():
        """启动数据包捕获"""
        try:
            if packet_capture.is_running:
                return jsonify({'message': '数据包捕获已在运行中'})
            
            data = request.get_json() or {}
            interface = data.get('interface')
            bpf_filter = data.get('bpf_filter')
            
            packet_capture.start_capture(interface=interface, bpf_filter=bpf_filter)
            
            return jsonify({'message': '数据包捕获已启动'})
        except Exception as e:
            app_logger.error(f"启动数据包捕获失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/capture/stop', methods=['POST'])
    def api_stop_capture():
        """停止数据包捕获"""
        try:
            if not packet_capture.is_running:
                return jsonify({'message': '数据包捕获未在运行'})
            
            packet_capture.stop_capture()
            
            return jsonify({'message': '数据包捕获已停止'})
        except Exception as e:
            app_logger.error(f"停止数据包捕获失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/config')
    def api_config():
        """获取系统配置"""
        try:
            # 返回部分配置信息（隐藏敏感信息）
            safe_config = {
                'system': config.get('system'),
                'network': {
                    'interfaces': config.get('network.interfaces'),
                    'bpf_filter': config.get('network.bpf_filter')
                },
                'storage': {
                    'pcap': {
                        'base_path': config.get('storage.pcap.base_path'),
                        'retention_days': config.get('storage.pcap.retention_days')
                    }
                },
                'detection': {
                    'rules': {
                        'enabled': config.get('detection.rules.enabled')
                    }
                }
            }
            
            return jsonify(safe_config)
        except Exception as e:
            app_logger.error(f"获取配置失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    @app.route('/api/alerts/recent')
    def api_recent_alerts():
        """获取最近的告警"""
        try:
            # 获取最近24小时的高危安全事件
            events = db_manager.get_security_events(
                limit=10,
                severity='high'
            )
            
            alerts = []
            for event in events:
                alerts.append({
                    'id': event['id'],
                    'title': event['title'],
                    'severity': event['severity'],
                    'src_ip': event['src_ip'],
                    'dst_ip': event['dst_ip'],
                    'attack_type': event['attack_type'],
                    'timestamp': event['created_at']
                })
            
            return jsonify({'alerts': alerts})
        except Exception as e:
            app_logger.error(f"获取最近告警失败: {e}")
            return jsonify({'error': str(e)}), 500
    
    def _get_uptime():
        """获取系统运行时间"""
        # 这里应该记录系统启动时间并计算运行时间
        # 为了演示，返回固定值
        return "2小时30分钟"


def run_web_server():
    """运行Web服务器"""
    app = create_app()
    
    host = config.get('web.host', '0.0.0.0')
    port = config.get('web.port', 8080)
    debug = config.get('system.debug', False)
    
    app_logger.info(f"启动Web服务器: http://{host}:{port}")
    
    app.run(host=host, port=port, debug=debug, threaded=True)


if __name__ == '__main__':
    run_web_server()
