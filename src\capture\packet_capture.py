"""
数据包捕获模块
使用scapy进行网络数据包的实时捕获
"""

import time
import threading
from typing import Callable, Optional, List, Dict, Any
from queue import Queue, Empty
from scapy.all import sniff, get_if_list, Packet
from scapy.layers.inet import IP, TCP, UDP, ICMP
from scapy.layers.inet6 import IPv6
from scapy.layers.l2 import Ether

from ..core.config import config
from ..core.logger import app_logger


class PacketCapture:
    """数据包捕获器"""
    
    def __init__(self):
        """初始化数据包捕获器"""
        self.is_running = False
        self.capture_thread: Optional[threading.Thread] = None
        self.packet_queue = Queue(maxsize=config.get('performance.queue.max_size', 10000))
        self.packet_handlers: List[Callable[[Packet], None]] = []
        self.stats = {
            'total_packets': 0,
            'tcp_packets': 0,
            'udp_packets': 0,
            'icmp_packets': 0,
            'other_packets': 0,
            'start_time': None,
            'last_packet_time': None
        }
        
        app_logger.info("数据包捕获器初始化完成")
    
    def add_packet_handler(self, handler: Callable[[Packet], None]) -> None:
        """
        添加数据包处理器
        
        Args:
            handler: 数据包处理函数
        """
        self.packet_handlers.append(handler)
        app_logger.info(f"添加数据包处理器: {handler.__name__}")
    
    def remove_packet_handler(self, handler: Callable[[Packet], None]) -> None:
        """
        移除数据包处理器
        
        Args:
            handler: 数据包处理函数
        """
        if handler in self.packet_handlers:
            self.packet_handlers.remove(handler)
            app_logger.info(f"移除数据包处理器: {handler.__name__}")
    
    def _packet_callback(self, packet: Packet) -> None:
        """
        数据包回调函数
        
        Args:
            packet: 捕获的数据包
        """
        try:
            # 更新统计信息
            self._update_stats(packet)
            
            # 将数据包放入队列
            if not self.packet_queue.full():
                self.packet_queue.put(packet, block=False)
            else:
                app_logger.warning("数据包队列已满，丢弃数据包")
            
            # 调用所有处理器
            for handler in self.packet_handlers:
                try:
                    handler(packet)
                except Exception as e:
                    app_logger.error(f"数据包处理器 {handler.__name__} 执行失败: {e}")
        
        except Exception as e:
            app_logger.error(f"数据包回调处理失败: {e}")
    
    def _update_stats(self, packet: Packet) -> None:
        """
        更新统计信息
        
        Args:
            packet: 数据包
        """
        self.stats['total_packets'] += 1
        self.stats['last_packet_time'] = time.time()
        
        if self.stats['start_time'] is None:
            self.stats['start_time'] = time.time()
        
        # 协议统计
        if packet.haslayer(TCP):
            self.stats['tcp_packets'] += 1
        elif packet.haslayer(UDP):
            self.stats['udp_packets'] += 1
        elif packet.haslayer(ICMP):
            self.stats['icmp_packets'] += 1
        else:
            self.stats['other_packets'] += 1
    
    def start_capture(self, interface: Optional[str] = None, 
                     bpf_filter: Optional[str] = None) -> None:
        """
        开始数据包捕获
        
        Args:
            interface: 网络接口名称，None表示使用配置文件中的接口
            bpf_filter: BPF过滤规则，None表示使用配置文件中的规则
        """
        if self.is_running:
            app_logger.warning("数据包捕获已在运行中")
            return
        
        # 获取配置
        if interface is None:
            interfaces = config.get('network.interfaces', ['any'])
            interface = interfaces[0] if interfaces else 'any'
        
        if bpf_filter is None:
            bpf_filter = config.get('network.bpf_filter', '')
        
        # 验证网络接口
        if interface != 'any' and interface not in get_if_list():
            app_logger.error(f"网络接口 {interface} 不存在")
            available_interfaces = get_if_list()
            app_logger.info(f"可用接口: {available_interfaces}")
            return
        
        self.is_running = True
        self.stats['start_time'] = time.time()
        
        app_logger.info(f"开始在接口 {interface} 上捕获数据包")
        if bpf_filter:
            app_logger.info(f"使用BPF过滤规则: {bpf_filter}")
        
        # 启动捕获线程
        self.capture_thread = threading.Thread(
            target=self._capture_worker,
            args=(interface, bpf_filter),
            daemon=True
        )
        self.capture_thread.start()
    
    def _capture_worker(self, interface: str, bpf_filter: str) -> None:
        """
        捕获工作线程
        
        Args:
            interface: 网络接口
            bpf_filter: BPF过滤规则
        """
        try:
            # 获取捕获配置
            capture_config = config.get('network.capture', {})
            
            sniff(
                iface=interface if interface != 'any' else None,
                filter=bpf_filter if bpf_filter else None,
                prn=self._packet_callback,
                store=False,  # 不存储在内存中
                stop_filter=lambda x: not self.is_running,
                timeout=capture_config.get('timeout', 1000) / 1000.0,  # 转换为秒
                count=capture_config.get('max_packets', 0) or 0
            )
        except Exception as e:
            app_logger.error(f"数据包捕获失败: {e}")
        finally:
            self.is_running = False
            app_logger.info("数据包捕获已停止")
    
    def stop_capture(self) -> None:
        """停止数据包捕获"""
        if not self.is_running:
            app_logger.warning("数据包捕获未在运行")
            return
        
        app_logger.info("正在停止数据包捕获...")
        self.is_running = False
        
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=5)
        
        app_logger.info("数据包捕获已停止")
    
    def get_packet(self, timeout: float = 1.0) -> Optional[Packet]:
        """
        从队列中获取数据包
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            数据包或None
        """
        try:
            return self.packet_queue.get(timeout=timeout)
        except Empty:
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取捕获统计信息
        
        Returns:
            统计信息字典
        """
        stats = self.stats.copy()
        
        # 计算运行时间和速率
        if stats['start_time']:
            runtime = time.time() - stats['start_time']
            stats['runtime_seconds'] = runtime
            stats['packets_per_second'] = stats['total_packets'] / runtime if runtime > 0 else 0
        
        stats['is_running'] = self.is_running
        stats['queue_size'] = self.packet_queue.qsize()
        
        return stats
    
    def clear_stats(self) -> None:
        """清除统计信息"""
        self.stats = {
            'total_packets': 0,
            'tcp_packets': 0,
            'udp_packets': 0,
            'icmp_packets': 0,
            'other_packets': 0,
            'start_time': time.time() if self.is_running else None,
            'last_packet_time': None
        }
        app_logger.info("统计信息已清除")


# 全局数据包捕获器实例
packet_capture = PacketCapture()
