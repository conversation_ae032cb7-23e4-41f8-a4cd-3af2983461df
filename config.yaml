# 网络流量分析与安全检测系统配置文件

# 系统基础配置
system:
  name: "网络流量分析与安全检测系统"
  version: "1.0.0"
  debug: true
  log_level: "INFO"

# 网络接口配置
network:
  # 监听接口列表
  interfaces:
    - "eth0"
    - "any"  # 监听所有接口
  
  # BPF过滤规则
  bpf_filter: ""  # 空表示捕获所有流量
  
  # 数据包捕获配置
  capture:
    buffer_size: 65536  # 缓冲区大小
    timeout: 1000       # 超时时间(ms)
    promiscuous: true   # 混杂模式
    max_packets: 0      # 0表示无限制

# 存储配置
storage:
  # PCAP文件存储
  pcap:
    base_path: "./data/pcap"
    max_file_size: "100MB"
    retention_days: 30
    compression: true
  
  # 数据库配置
  database:
    type: "sqlite"  # sqlite, mysql, postgresql
    url: "sqlite:///./data/traffic_analysis.db"
    pool_size: 10
    max_overflow: 20
  
  # Redis配置（用于缓存和队列）
  redis:
    host: "localhost"
    port: 6379
    db: 0
    password: ""

# 检测引擎配置
detection:
  # 规则引擎
  rules:
    enabled: true
    rules_path: "./rules"
    update_interval: 3600  # 规则更新间隔(秒)
  
  # 异常检测
  anomaly:
    enabled: true
    algorithms:
      - "isolation_forest"
      - "one_class_svm"
    threshold: 0.1
  
  # 威胁情报
  threat_intel:
    enabled: false
    virustotal_api_key: ""
    update_interval: 86400  # 24小时

# Web界面配置
web:
  host: "0.0.0.0"
  port: 8080
  secret_key: "your-secret-key-here"
  
  # 认证配置
  auth:
    enabled: true
    session_timeout: 3600

# 告警配置
alerts:
  # 邮件告警
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: ""
    password: ""
    from_addr: ""
    to_addrs: []
  
  # Webhook告警
  webhook:
    enabled: false
    url: ""
    timeout: 30

# 性能配置
performance:
  # 工作进程数
  workers: 4
  
  # 队列配置
  queue:
    max_size: 10000
    batch_size: 100
  
  # 内存限制
  memory_limit: "2GB"
