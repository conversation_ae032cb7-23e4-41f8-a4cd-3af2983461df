"""
规则引擎模块
基于规则的异常和攻击检测
"""

import re
import json
import time
import hashlib
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from dataclasses import dataclass

from ..core.config import config
from ..core.logger import app_logger, security_logger


@dataclass
class DetectionRule:
    """检测规则数据类"""
    id: str
    name: str
    description: str
    severity: str  # low, medium, high, critical
    category: str  # malware, ddos, scan, injection, etc.
    enabled: bool
    conditions: List[Dict[str, Any]]
    actions: List[str]
    metadata: Dict[str, Any]


class RuleEngine:
    """规则引擎"""
    
    def __init__(self):
        """初始化规则引擎"""
        self.rules: Dict[str, DetectionRule] = {}
        self.rule_stats: Dict[str, Dict[str, Any]] = {}
        self.rules_path = Path(config.get('detection.rules.rules_path', './rules'))
        
        # 创建规则目录
        self.rules_path.mkdir(parents=True, exist_ok=True)
        
        # 加载规则
        self.load_rules()
        
        # 条件检查函数映射
        self.condition_checkers = {
            'ip_match': self._check_ip_match,
            'port_match': self._check_port_match,
            'protocol_match': self._check_protocol_match,
            'payload_contains': self._check_payload_contains,
            'payload_regex': self._check_payload_regex,
            'packet_size': self._check_packet_size,
            'tcp_flags': self._check_tcp_flags,
            'dns_query': self._check_dns_query,
            'http_method': self._check_http_method,
            'http_uri': self._check_http_uri,
            'frequency': self._check_frequency,
            'threshold': self._check_threshold
        }
        
        # 频率统计
        self.frequency_counters: Dict[str, Dict[str, Any]] = {}
        
        app_logger.info(f"规则引擎初始化完成，加载 {len(self.rules)} 条规则")
    
    def load_rules(self) -> None:
        """加载检测规则"""
        try:
            # 加载内置规则
            self._load_builtin_rules()
            
            # 加载自定义规则文件
            for rule_file in self.rules_path.glob("*.json"):
                self._load_rule_file(rule_file)
            
            app_logger.info(f"规则加载完成，共 {len(self.rules)} 条规则")
            
        except Exception as e:
            app_logger.error(f"加载规则失败: {e}")
    
    def _load_builtin_rules(self) -> None:
        """加载内置规则"""
        builtin_rules = [
            {
                "id": "ddos_syn_flood",
                "name": "SYN洪水攻击检测",
                "description": "检测TCP SYN洪水攻击",
                "severity": "high",
                "category": "ddos",
                "enabled": True,
                "conditions": [
                    {"type": "protocol_match", "value": "TCP"},
                    {"type": "tcp_flags", "value": {"syn": True, "ack": False}},
                    {"type": "frequency", "key": "src_ip", "threshold": 100, "window": 60}
                ],
                "actions": ["alert", "log"],
                "metadata": {"attack_type": "syn_flood", "mitre_id": "T1499.001"}
            },
            {
                "id": "port_scan_detection",
                "name": "端口扫描检测",
                "description": "检测端口扫描行为",
                "severity": "medium",
                "category": "scan",
                "enabled": True,
                "conditions": [
                    {"type": "tcp_flags", "value": {"syn": True, "ack": False}},
                    {"type": "frequency", "key": "src_ip_dst_port", "threshold": 20, "window": 30}
                ],
                "actions": ["alert", "log"],
                "metadata": {"attack_type": "port_scan", "mitre_id": "T1046"}
            },
            {
                "id": "dns_tunneling",
                "name": "DNS隧道检测",
                "description": "检测DNS隧道通信",
                "severity": "high",
                "category": "exfiltration",
                "enabled": True,
                "conditions": [
                    {"type": "protocol_match", "value": "UDP"},
                    {"type": "port_match", "value": 53},
                    {"type": "dns_query", "min_length": 50},
                    {"type": "frequency", "key": "src_ip", "threshold": 50, "window": 60}
                ],
                "actions": ["alert", "log"],
                "metadata": {"attack_type": "dns_tunneling", "mitre_id": "T1071.004"}
            },
            {
                "id": "sql_injection",
                "name": "SQL注入检测",
                "description": "检测HTTP请求中的SQL注入攻击",
                "severity": "high",
                "category": "injection",
                "enabled": True,
                "conditions": [
                    {"type": "protocol_match", "value": "TCP"},
                    {"type": "port_match", "value": 80},
                    {"type": "payload_regex", "pattern": r"(?i)(union|select|insert|update|delete|drop|exec|script).*(\s|%20)+(or|and).*(\s|%20)*="}
                ],
                "actions": ["alert", "log", "block"],
                "metadata": {"attack_type": "sql_injection", "mitre_id": "T1190"}
            },
            {
                "id": "large_packet_anomaly",
                "name": "异常大数据包检测",
                "description": "检测异常大的数据包",
                "severity": "low",
                "category": "anomaly",
                "enabled": True,
                "conditions": [
                    {"type": "packet_size", "operator": ">", "value": 9000}
                ],
                "actions": ["log"],
                "metadata": {"attack_type": "anomaly"}
            }
        ]
        
        for rule_data in builtin_rules:
            rule = DetectionRule(**rule_data)
            self.rules[rule.id] = rule
            self.rule_stats[rule.id] = {
                'matches': 0,
                'last_match': None,
                'false_positives': 0
            }
    
    def _load_rule_file(self, rule_file: Path) -> None:
        """
        加载规则文件
        
        Args:
            rule_file: 规则文件路径
        """
        try:
            with open(rule_file, 'r', encoding='utf-8') as f:
                rule_data = json.load(f)
            
            rule = DetectionRule(**rule_data)
            self.rules[rule.id] = rule
            self.rule_stats[rule.id] = {
                'matches': 0,
                'last_match': None,
                'false_positives': 0
            }
            
            app_logger.info(f"加载规则文件: {rule_file}")
            
        except Exception as e:
            app_logger.error(f"加载规则文件 {rule_file} 失败: {e}")
    
    def check_packet(self, packet_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        检查数据包是否匹配规则
        
        Args:
            packet_data: 数据包数据
            
        Returns:
            匹配的规则和检测结果列表
        """
        matches = []
        
        for rule_id, rule in self.rules.items():
            if not rule.enabled:
                continue
            
            try:
                if self._check_rule_conditions(rule, packet_data):
                    match_result = {
                        'rule_id': rule_id,
                        'rule_name': rule.name,
                        'severity': rule.severity,
                        'category': rule.category,
                        'description': rule.description,
                        'timestamp': datetime.now().isoformat(),
                        'packet_data': packet_data,
                        'metadata': rule.metadata,
                        'actions': rule.actions
                    }
                    
                    matches.append(match_result)
                    
                    # 更新统计信息
                    self.rule_stats[rule_id]['matches'] += 1
                    self.rule_stats[rule_id]['last_match'] = datetime.now().isoformat()
                    
                    # 记录安全事件
                    security_logger(
                        f"规则匹配: {rule.name}",
                        rule_id=rule_id,
                        severity=rule.severity,
                        src_ip=packet_data.get('src_ip'),
                        dst_ip=packet_data.get('dst_ip')
                    )
            
            except Exception as e:
                app_logger.error(f"检查规则 {rule_id} 失败: {e}")
        
        return matches
    
    def _check_rule_conditions(self, rule: DetectionRule, packet_data: Dict[str, Any]) -> bool:
        """
        检查规则条件
        
        Args:
            rule: 检测规则
            packet_data: 数据包数据
            
        Returns:
            是否匹配
        """
        for condition in rule.conditions:
            condition_type = condition.get('type')
            
            if condition_type not in self.condition_checkers:
                app_logger.warning(f"未知的条件类型: {condition_type}")
                continue
            
            checker = self.condition_checkers[condition_type]
            if not checker(condition, packet_data):
                return False
        
        return True
    
    def _check_ip_match(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查IP匹配条件"""
        target_ip = condition.get('value')
        field = condition.get('field', 'src_ip')
        
        packet_ip = packet_data.get(field)
        if not packet_ip:
            return False
        
        # 支持CIDR格式
        if '/' in target_ip:
            # 简单的CIDR匹配实现
            return packet_ip.startswith(target_ip.split('/')[0])
        else:
            return packet_ip == target_ip
    
    def _check_port_match(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查端口匹配条件"""
        target_port = condition.get('value')
        field = condition.get('field', 'dst_port')
        
        packet_port = packet_data.get(field)
        if packet_port is None:
            return False
        
        if isinstance(target_port, list):
            return packet_port in target_port
        else:
            return packet_port == target_port
    
    def _check_protocol_match(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查协议匹配条件"""
        target_protocol = condition.get('value').upper()
        packet_protocol = packet_data.get('protocol', '').upper()
        
        return packet_protocol == target_protocol
    
    def _check_payload_contains(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查载荷包含条件"""
        target_string = condition.get('value')
        payload = packet_data.get('raw_data', '')
        
        if condition.get('case_sensitive', False):
            return target_string in payload
        else:
            return target_string.lower() in payload.lower()
    
    def _check_payload_regex(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查载荷正则表达式条件"""
        pattern = condition.get('pattern')
        payload = packet_data.get('raw_data', '')
        
        try:
            flags = re.IGNORECASE if not condition.get('case_sensitive', False) else 0
            return bool(re.search(pattern, payload, flags))
        except re.error as e:
            app_logger.error(f"正则表达式错误: {e}")
            return False
    
    def _check_packet_size(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查数据包大小条件"""
        operator = condition.get('operator', '==')
        target_size = condition.get('value')
        packet_size = packet_data.get('packet_size', 0)
        
        if operator == '>':
            return packet_size > target_size
        elif operator == '<':
            return packet_size < target_size
        elif operator == '>=':
            return packet_size >= target_size
        elif operator == '<=':
            return packet_size <= target_size
        else:
            return packet_size == target_size
    
    def _check_tcp_flags(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查TCP标志位条件"""
        target_flags = condition.get('value')
        
        tcp_layer = packet_data.get('layers', {}).get('tcp', {})
        packet_flags = tcp_layer.get('flags', {})
        
        for flag, expected in target_flags.items():
            if packet_flags.get(flag, False) != expected:
                return False
        
        return True
    
    def _check_dns_query(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查DNS查询条件"""
        dns_layer = packet_data.get('layers', {}).get('dns', {})
        queries = dns_layer.get('queries', [])
        
        if not queries:
            return False
        
        min_length = condition.get('min_length', 0)
        
        for query in queries:
            query_name = query.get('name', '')
            if len(query_name) >= min_length:
                return True
        
        return False
    
    def _check_http_method(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查HTTP方法条件"""
        target_method = condition.get('value').upper()
        
        http_layer = packet_data.get('layers', {}).get('http', {})
        method = http_layer.get('method', '').upper()
        
        return method == target_method
    
    def _check_http_uri(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查HTTP URI条件"""
        pattern = condition.get('pattern')
        
        http_layer = packet_data.get('layers', {}).get('http', {})
        uri = http_layer.get('uri', '')
        
        try:
            return bool(re.search(pattern, uri, re.IGNORECASE))
        except re.error:
            return False
    
    def _check_frequency(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查频率条件"""
        key_template = condition.get('key')
        threshold = condition.get('threshold')
        window = condition.get('window', 60)  # 默认60秒窗口
        
        # 构建计数键
        if key_template == 'src_ip':
            count_key = packet_data.get('src_ip')
        elif key_template == 'src_ip_dst_port':
            count_key = f"{packet_data.get('src_ip')}:{packet_data.get('dst_port')}"
        else:
            count_key = key_template
        
        if not count_key:
            return False
        
        current_time = time.time()
        
        # 初始化计数器
        if count_key not in self.frequency_counters:
            self.frequency_counters[count_key] = {
                'count': 0,
                'window_start': current_time,
                'timestamps': []
            }
        
        counter = self.frequency_counters[count_key]
        
        # 清理过期的时间戳
        counter['timestamps'] = [
            ts for ts in counter['timestamps'] 
            if current_time - ts <= window
        ]
        
        # 添加当前时间戳
        counter['timestamps'].append(current_time)
        counter['count'] = len(counter['timestamps'])
        
        return counter['count'] >= threshold
    
    def _check_threshold(self, condition: Dict[str, Any], packet_data: Dict[str, Any]) -> bool:
        """检查阈值条件"""
        # 这里可以实现更复杂的阈值检查逻辑
        return True
    
    def get_rule_stats(self) -> Dict[str, Any]:
        """获取规则统计信息"""
        return {
            'total_rules': len(self.rules),
            'enabled_rules': len([r for r in self.rules.values() if r.enabled]),
            'rule_stats': self.rule_stats
        }


# 全局规则引擎实例
rule_engine = RuleEngine()
