#!/usr/bin/env python3
"""
系统安装脚本
自动创建必要的目录和初始化配置
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python版本检查通过: {sys.version}")
    return True


def create_directories():
    """创建必要的目录"""
    print("\n创建目录结构...")
    
    directories = [
        "data",
        "data/pcap",
        "logs",
        "rules"
    ]
    
    for directory in directories:
        path = Path(directory)
        path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")


def install_dependencies():
    """安装依赖包"""
    print("\n安装依赖包...")
    
    try:
        # 检查是否在虚拟环境中
        in_venv = hasattr(sys, 'real_prefix') or (
            hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
        )
        
        if not in_venv:
            print("⚠️  建议在虚拟环境中安装依赖包")
            response = input("是否继续安装? (y/N): ")
            if response.lower() != 'y':
                print("安装已取消")
                return False
        
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✅ pip已升级")
        
        # 安装依赖包
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False
    except FileNotFoundError:
        print("❌ requirements.txt文件不存在")
        return False


def check_permissions():
    """检查权限"""
    print("\n检查系统权限...")
    
    if os.name == 'nt':  # Windows
        try:
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if is_admin:
                print("✅ 检测到管理员权限")
            else:
                print("⚠️  未检测到管理员权限，数据包捕获功能可能受限")
        except:
            print("⚠️  无法检测权限状态")
    else:  # Linux/macOS
        if os.geteuid() == 0:
            print("✅ 检测到root权限")
        else:
            print("⚠️  未检测到root权限，数据包捕获功能可能受限")
            print("提示: 使用 sudo python main.py 运行系统")


def check_network_interfaces():
    """检查网络接口"""
    print("\n检查网络接口...")
    
    try:
        from scapy.all import get_if_list
        interfaces = get_if_list()
        
        print("可用网络接口:")
        for i, interface in enumerate(interfaces, 1):
            print(f"  {i}. {interface}")
        
        if interfaces:
            print("✅ 网络接口检查完成")
        else:
            print("⚠️  未找到可用的网络接口")
            
    except ImportError:
        print("⚠️  Scapy未安装，无法检查网络接口")
    except Exception as e:
        print(f"⚠️  检查网络接口时出错: {e}")


def create_sample_rules():
    """创建示例规则文件"""
    print("\n创建示例规则文件...")
    
    sample_rule = {
        "id": "sample_custom_rule",
        "name": "示例自定义规则",
        "description": "这是一个示例自定义规则，检测特定IP的HTTP访问",
        "severity": "medium",
        "category": "custom",
        "enabled": False,
        "conditions": [
            {"type": "protocol_match", "value": "TCP"},
            {"type": "port_match", "field": "dst_port", "value": 80},
            {"type": "ip_match", "field": "src_ip", "value": "*************"}
        ],
        "actions": ["alert", "log"],
        "metadata": {
            "attack_type": "custom_access",
            "description": "特定IP的HTTP访问"
        }
    }
    
    import json
    rules_file = Path("rules/sample_rule.json")
    
    try:
        with open(rules_file, 'w', encoding='utf-8') as f:
            json.dump(sample_rule, f, indent=2, ensure_ascii=False)
        print(f"✅ 创建示例规则文件: {rules_file}")
    except Exception as e:
        print(f"❌ 创建示例规则文件失败: {e}")


def run_system_test():
    """运行系统测试"""
    print("\n运行系统测试...")
    
    try:
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 系统测试通过")
        else:
            print("⚠️  系统测试发现问题:")
            print(result.stdout)
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
                
    except subprocess.TimeoutExpired:
        print("⚠️  系统测试超时")
    except Exception as e:
        print(f"⚠️  运行系统测试时出错: {e}")


def print_usage_instructions():
    """打印使用说明"""
    print("\n" + "=" * 60)
    print("安装完成！")
    print("=" * 60)
    
    print("\n使用说明:")
    print("1. 启动完整系统（需要管理员权限）:")
    if os.name == 'nt':
        print("   以管理员身份运行: python main.py")
    else:
        print("   sudo python main.py")
    
    print("\n2. 仅启动Web管理界面:")
    print("   python run_web.py")
    print("   然后访问: http://localhost:8080")
    
    print("\n3. 运行系统测试:")
    print("   python test_system.py")
    
    print("\n4. 配置文件:")
    print("   编辑 config.yaml 文件来修改系统配置")
    
    print("\n5. 自定义规则:")
    print("   在 rules/ 目录下添加JSON格式的规则文件")
    
    print("\n注意事项:")
    print("- 数据包捕获功能需要管理员/root权限")
    print("- 确保防火墙允许Web服务端口(8080)")
    print("- 定期清理logs/和data/目录下的文件")


def main():
    """主安装函数"""
    print("网络流量分析与安全检测系统 - 安装脚本")
    print("=" * 60)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 安装依赖
    if not install_dependencies():
        print("\n❌ 安装失败，请检查错误信息")
        sys.exit(1)
    
    # 检查权限
    check_permissions()
    
    # 检查网络接口
    check_network_interfaces()
    
    # 创建示例规则
    create_sample_rules()
    
    # 运行系统测试
    run_system_test()
    
    # 打印使用说明
    print_usage_instructions()


if __name__ == "__main__":
    main()
