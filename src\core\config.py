"""
配置管理模块
负责加载和管理系统配置
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)
        self._config: Dict[str, Any] = {}
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f) or {}
            else:
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self._config = self._get_default_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save_config(self) -> None:
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self._config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'system': {
                'name': '网络流量分析与安全检测系统',
                'version': '1.0.0',
                'debug': True,
                'log_level': 'INFO'
            },
            'network': {
                'interfaces': ['any'],
                'bpf_filter': '',
                'capture': {
                    'buffer_size': 65536,
                    'timeout': 1000,
                    'promiscuous': True,
                    'max_packets': 0
                }
            },
            'storage': {
                'pcap': {
                    'base_path': './data/pcap',
                    'max_file_size': '100MB',
                    'retention_days': 30,
                    'compression': True
                },
                'database': {
                    'type': 'sqlite',
                    'url': 'sqlite:///./data/traffic_analysis.db'
                }
            },
            'web': {
                'host': '0.0.0.0',
                'port': 8080,
                'secret_key': 'default-secret-key'
            }
        }
    
    @property
    def config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self._config


# 全局配置实例
config = ConfigManager()
