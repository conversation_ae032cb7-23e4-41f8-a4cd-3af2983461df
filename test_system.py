#!/usr/bin/env python3
"""
系统功能测试脚本
用于验证各个模块的基本功能
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.core.config import config
from src.core.logger import app_logger
from src.capture.packet_capture import packet_capture
from src.capture.protocol_parser import protocol_parser
from src.storage.pcap_storage import pcap_storage
from src.storage.database import db_manager
from src.detection.rule_engine import rule_engine


def test_config():
    """测试配置管理"""
    print("=" * 50)
    print("测试配置管理模块")
    print("=" * 50)
    
    # 测试配置读取
    system_name = config.get('system.name')
    print(f"系统名称: {system_name}")
    
    # 测试配置设置
    config.set('test.value', 'test_data')
    test_value = config.get('test.value')
    print(f"测试配置值: {test_value}")
    
    print("✅ 配置管理模块测试通过\n")


def test_logger():
    """测试日志系统"""
    print("=" * 50)
    print("测试日志系统")
    print("=" * 50)
    
    app_logger.info("这是一条信息日志")
    app_logger.warning("这是一条警告日志")
    app_logger.error("这是一条错误日志")
    
    print("✅ 日志系统测试通过\n")


def test_protocol_parser():
    """测试协议解析器"""
    print("=" * 50)
    print("测试协议解析器")
    print("=" * 50)
    
    try:
        from scapy.all import IP, TCP, Ether
        
        # 创建测试数据包
        packet = Ether() / IP(src="***********00", dst="***********") / TCP(sport=12345, dport=80)
        
        # 解析数据包
        parsed_data = protocol_parser.parse_packet(packet)
        
        print(f"解析结果:")
        print(f"  源IP: {parsed_data.get('src_ip')}")
        print(f"  目标IP: {parsed_data.get('dst_ip')}")
        print(f"  协议: {parsed_data.get('protocol')}")
        print(f"  源端口: {parsed_data.get('src_port')}")
        print(f"  目标端口: {parsed_data.get('dst_port')}")
        
        print("✅ 协议解析器测试通过\n")
        
    except ImportError:
        print("❌ Scapy未安装，跳过协议解析器测试\n")
    except Exception as e:
        print(f"❌ 协议解析器测试失败: {e}\n")


def test_rule_engine():
    """测试规则引擎"""
    print("=" * 50)
    print("测试规则引擎")
    print("=" * 50)
    
    # 创建测试数据包数据
    packet_data = {
        'src_ip': '***********00',
        'dst_ip': '***********',
        'src_port': 12345,
        'dst_port': 80,
        'protocol': 'TCP',
        'packet_size': 1500,
        'layers': {
            'tcp': {
                'flags': {
                    'syn': True,
                    'ack': False
                }
            }
        }
    }
    
    # 测试规则检查
    matches = rule_engine.check_packet(packet_data)
    
    print(f"规则匹配结果: {len(matches)} 条规则匹配")
    for match in matches:
        print(f"  - {match['rule_name']} ({match['severity']})")
    
    # 显示规则统计
    stats = rule_engine.get_rule_stats()
    print(f"规则统计: 总计 {stats['total_rules']} 条规则，{stats['enabled_rules']} 条启用")
    
    print("✅ 规则引擎测试通过\n")


def test_database():
    """测试数据库"""
    print("=" * 50)
    print("测试数据库模块")
    print("=" * 50)
    
    try:
        # 测试数据库连接
        session = db_manager.get_session()
        session.close()
        print("数据库连接正常")
        
        # 测试保存数据包记录
        packet_data = {
            'session_id': 'test_session_001',
            'timestamp': '2024-01-01T12:00:00',
            'src_ip': '***********00',
            'dst_ip': '***********',
            'src_port': 12345,
            'dst_port': 80,
            'protocol': 'TCP',
            'packet_size': 1500
        }
        
        record_id = db_manager.save_packet_record(packet_data)
        if record_id:
            print(f"数据包记录保存成功，ID: {record_id}")
        
        # 测试保存安全事件
        event_data = {
            'event_id': 'test_event_001',
            'event_type': 'test',
            'severity': 'medium',
            'title': '测试安全事件',
            'description': '这是一个测试安全事件',
            'src_ip': '***********00',
            'dst_ip': '***********',
            'first_seen': '2024-01-01T12:00:00'
        }
        
        event_id = db_manager.save_security_event(event_data)
        if event_id:
            print(f"安全事件保存成功，ID: {event_id}")
        
        # 测试查询安全事件
        events = db_manager.get_security_events(limit=5)
        print(f"查询到 {len(events)} 条安全事件")
        
        print("✅ 数据库模块测试通过\n")
        
    except Exception as e:
        print(f"❌ 数据库模块测试失败: {e}\n")


def test_pcap_storage():
    """测试PCAP存储"""
    print("=" * 50)
    print("测试PCAP存储模块")
    print("=" * 50)
    
    try:
        # 获取存储统计
        stats = pcap_storage.get_stats()
        print(f"PCAP存储统计:")
        print(f"  创建文件数: {stats['files_created']}")
        print(f"  写入数据包数: {stats['packets_written']}")
        print(f"  写入字节数: {stats['bytes_written']}")
        
        # 获取文件列表
        files = pcap_storage.get_file_list()
        print(f"PCAP文件数量: {len(files)}")
        
        print("✅ PCAP存储模块测试通过\n")
        
    except Exception as e:
        print(f"❌ PCAP存储模块测试失败: {e}\n")


def test_packet_capture():
    """测试数据包捕获（模拟）"""
    print("=" * 50)
    print("测试数据包捕获模块")
    print("=" * 50)
    
    try:
        # 获取捕获统计
        stats = packet_capture.get_stats()
        print(f"数据包捕获统计:")
        print(f"  总数据包数: {stats['total_packets']}")
        print(f"  TCP数据包数: {stats['tcp_packets']}")
        print(f"  UDP数据包数: {stats['udp_packets']}")
        print(f"  运行状态: {stats['is_running']}")
        
        print("✅ 数据包捕获模块测试通过\n")
        
    except Exception as e:
        print(f"❌ 数据包捕获模块测试失败: {e}\n")


def test_web_api():
    """测试Web API"""
    print("=" * 50)
    print("测试Web API")
    print("=" * 50)
    
    try:
        import requests
        
        # 启动Web服务器（在后台线程中）
        from src.web.app import create_app
        app = create_app()
        
        def run_server():
            app.run(host='127.0.0.1', port=8081, debug=False)
        
        server_thread = threading.Thread(target=run_server, daemon=True)
        server_thread.start()
        
        # 等待服务器启动
        time.sleep(2)
        
        # 测试API端点
        base_url = 'http://127.0.0.1:8081'
        
        # 测试状态API
        response = requests.get(f'{base_url}/api/status', timeout=5)
        if response.status_code == 200:
            print("✅ 状态API测试通过")
        else:
            print(f"❌ 状态API测试失败: {response.status_code}")
        
        # 测试安全事件API
        response = requests.get(f'{base_url}/api/security-events', timeout=5)
        if response.status_code == 200:
            print("✅ 安全事件API测试通过")
        else:
            print(f"❌ 安全事件API测试失败: {response.status_code}")
        
        print("✅ Web API测试通过\n")
        
    except ImportError:
        print("❌ requests库未安装，跳过Web API测试\n")
    except Exception as e:
        print(f"❌ Web API测试失败: {e}\n")


def main():
    """主测试函数"""
    print("网络流量分析与安全检测系统 - 功能测试")
    print("=" * 60)
    
    # 创建必要的目录
    Path("data").mkdir(exist_ok=True)
    Path("data/pcap").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    # 运行各项测试
    test_config()
    test_logger()
    test_protocol_parser()
    test_rule_engine()
    test_database()
    test_pcap_storage()
    test_packet_capture()
    test_web_api()
    
    print("=" * 60)
    print("所有测试完成！")
    print("\n使用说明:")
    print("1. 运行完整系统: python main.py")
    print("2. 仅运行Web界面: python run_web.py")
    print("3. 访问Web界面: http://localhost:8080")


if __name__ == "__main__":
    main()
