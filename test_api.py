#!/usr/bin/env python3
"""
API测试脚本
测试Web API接口是否正常工作
"""

import requests
import json
import sys

def test_api_endpoint(endpoint, description):
    """测试API端点"""
    try:
        url = f"http://localhost:8080/api/{endpoint}"
        print(f"测试 {description}: {url}")
        
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ 成功 - 状态码: {response.status_code}")
            
            # 显示部分数据
            if isinstance(data, dict):
                if 'events' in data:
                    print(f"  📊 事件数量: {len(data['events'])}")
                elif 'rules' in data:
                    print(f"  📊 规则数量: {len(data['rules'])}")
                elif 'files' in data:
                    print(f"  📊 文件数量: {len(data['files'])}")
                else:
                    print(f"  📊 数据键: {list(data.keys())}")
            
            return True
        else:
            print(f"  ❌ 失败 - 状态码: {response.status_code}")
            print(f"  错误信息: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"  ❌ 请求失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ 其他错误: {e}")
        return False

def main():
    """主测试函数"""
    print("网络流量分析与安全检测系统 - API测试")
    print("=" * 50)
    
    # 测试的API端点
    endpoints = [
        ("status", "系统状态"),
        ("security-events", "安全事件"),
        ("rules", "检测规则"),
        ("pcap-files", "PCAP文件"),
        ("config", "系统配置"),
        ("alerts/recent", "最近告警"),
        ("traffic-stats", "流量统计")
    ]
    
    success_count = 0
    total_count = len(endpoints)
    
    for endpoint, description in endpoints:
        if test_api_endpoint(endpoint, description):
            success_count += 1
        print()
    
    print("=" * 50)
    print(f"测试结果: {success_count}/{total_count} 个API端点正常")
    
    if success_count == total_count:
        print("🎉 所有API测试通过！")
        print("\n可以访问Web界面: http://localhost:8080")
    else:
        print("⚠️  部分API测试失败，请检查Web服务器状态")
    
    return success_count == total_count

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
