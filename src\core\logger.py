"""
日志管理模块
提供统一的日志记录功能
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional

from .config import config


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        """初始化日志管理器"""
        self._setup_logger()
    
    def _setup_logger(self) -> None:
        """设置日志配置"""
        # 移除默认处理器
        logger.remove()
        
        # 获取日志级别
        log_level = config.get('system.log_level', 'INFO')
        
        # 控制台输出
        logger.add(
            sys.stdout,
            level=log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            colorize=True
        )
        
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 文件输出 - 普通日志
        logger.add(
            log_dir / "app.log",
            level=log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 文件输出 - 错误日志
        logger.add(
            log_dir / "error.log",
            level="ERROR",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="10 MB",
            retention="30 days",
            compression="zip",
            encoding="utf-8"
        )
        
        # 文件输出 - 安全事件日志
        logger.add(
            log_dir / "security.log",
            level="WARNING",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            rotation="10 MB",
            retention="90 days",  # 安全日志保留更长时间
            compression="zip",
            encoding="utf-8",
            filter=lambda record: "SECURITY" in record["extra"]
        )
    
    def get_logger(self, name: Optional[str] = None):
        """
        获取日志记录器
        
        Args:
            name: 日志记录器名称
            
        Returns:
            日志记录器实例
        """
        if name:
            return logger.bind(name=name)
        return logger
    
    def log_security_event(self, message: str, **kwargs):
        """
        记录安全事件
        
        Args:
            message: 日志消息
            **kwargs: 额外的上下文信息
        """
        logger.bind(SECURITY=True).warning(f"SECURITY EVENT: {message}", **kwargs)


# 全局日志管理器实例
log_manager = LoggerManager()

# 便捷的日志记录器
app_logger = log_manager.get_logger("app")
security_logger = log_manager.log_security_event
