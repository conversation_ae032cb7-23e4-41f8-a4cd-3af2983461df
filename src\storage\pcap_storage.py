"""
PCAP文件存储模块
负责将捕获的数据包保存为PCAP格式文件
"""

import os
import gzip
import time
import threading
from pathlib import Path
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from scapy.all import Packet, wrpcap, PcapWriter
from queue import Queue, Empty

from ..core.config import config
from ..core.logger import app_logger


class PcapStorage:
    """PCAP文件存储器"""
    
    def __init__(self):
        """初始化PCAP存储器"""
        self.base_path = Path(config.get('storage.pcap.base_path', './data/pcap'))
        self.max_file_size = self._parse_size(config.get('storage.pcap.max_file_size', '100MB'))
        self.retention_days = config.get('storage.pcap.retention_days', 30)
        self.compression = config.get('storage.pcap.compression', True)
        
        # 创建存储目录
        self.base_path.mkdir(parents=True, exist_ok=True)
        
        # 当前写入的文件
        self.current_writer: Optional[PcapWriter] = None
        self.current_file_path: Optional[Path] = None
        self.current_file_size = 0
        self.current_file_start_time = None
        
        # 写入队列和线程
        self.write_queue = Queue(maxsize=10000)
        self.write_thread: Optional[threading.Thread] = None
        self.is_writing = False
        
        # 统计信息
        self.stats = {
            'files_created': 0,
            'packets_written': 0,
            'bytes_written': 0,
            'compression_ratio': 0.0
        }
        
        app_logger.info(f"PCAP存储器初始化完成，存储路径: {self.base_path}")
    
    def start_writing(self) -> None:
        """开始写入线程"""
        if self.is_writing:
            app_logger.warning("PCAP写入线程已在运行")
            return
        
        self.is_writing = True
        self.write_thread = threading.Thread(target=self._write_worker, daemon=True)
        self.write_thread.start()
        app_logger.info("PCAP写入线程已启动")
    
    def stop_writing(self) -> None:
        """停止写入线程"""
        if not self.is_writing:
            app_logger.warning("PCAP写入线程未在运行")
            return
        
        app_logger.info("正在停止PCAP写入线程...")
        self.is_writing = False
        
        # 等待队列清空
        while not self.write_queue.empty():
            time.sleep(0.1)
        
        if self.write_thread and self.write_thread.is_alive():
            self.write_thread.join(timeout=5)
        
        # 关闭当前文件
        self._close_current_file()
        
        app_logger.info("PCAP写入线程已停止")
    
    def write_packet(self, packet: Packet) -> None:
        """
        写入数据包到队列
        
        Args:
            packet: 要写入的数据包
        """
        if not self.is_writing:
            app_logger.warning("PCAP写入线程未启动")
            return
        
        try:
            self.write_queue.put(packet, block=False)
        except:
            app_logger.warning("PCAP写入队列已满，丢弃数据包")
    
    def _write_worker(self) -> None:
        """写入工作线程"""
        while self.is_writing:
            try:
                packet = self.write_queue.get(timeout=1.0)
                self._write_packet_to_file(packet)
            except Empty:
                continue
            except Exception as e:
                app_logger.error(f"写入PCAP文件失败: {e}")
    
    def _write_packet_to_file(self, packet: Packet) -> None:
        """
        将数据包写入文件
        
        Args:
            packet: 数据包
        """
        # 检查是否需要创建新文件
        if self._should_create_new_file():
            self._create_new_file()
        
        if self.current_writer:
            try:
                self.current_writer.write(packet)
                packet_size = len(packet)
                self.current_file_size += packet_size
                
                # 更新统计信息
                self.stats['packets_written'] += 1
                self.stats['bytes_written'] += packet_size
                
            except Exception as e:
                app_logger.error(f"写入数据包到PCAP文件失败: {e}")
    
    def _should_create_new_file(self) -> bool:
        """检查是否应该创建新文件"""
        if self.current_writer is None:
            return True
        
        # 检查文件大小
        if self.current_file_size >= self.max_file_size:
            return True
        
        # 检查时间（每小时创建新文件）
        if self.current_file_start_time:
            elapsed = time.time() - self.current_file_start_time
            if elapsed >= 3600:  # 1小时
                return True
        
        return False
    
    def _create_new_file(self) -> None:
        """创建新的PCAP文件"""
        # 关闭当前文件
        self._close_current_file()
        
        # 生成新文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"traffic_{timestamp}.pcap"
        self.current_file_path = self.base_path / filename
        
        try:
            self.current_writer = PcapWriter(str(self.current_file_path), append=False)
            self.current_file_size = 0
            self.current_file_start_time = time.time()
            self.stats['files_created'] += 1
            
            app_logger.info(f"创建新PCAP文件: {self.current_file_path}")
            
        except Exception as e:
            app_logger.error(f"创建PCAP文件失败: {e}")
            self.current_writer = None
            self.current_file_path = None
    
    def _close_current_file(self) -> None:
        """关闭当前文件"""
        if self.current_writer:
            try:
                self.current_writer.close()
                app_logger.info(f"关闭PCAP文件: {self.current_file_path}")
                
                # 压缩文件
                if self.compression and self.current_file_path:
                    self._compress_file(self.current_file_path)
                
            except Exception as e:
                app_logger.error(f"关闭PCAP文件失败: {e}")
            finally:
                self.current_writer = None
                self.current_file_path = None
                self.current_file_size = 0
                self.current_file_start_time = None
    
    def _compress_file(self, file_path: Path) -> None:
        """
        压缩PCAP文件
        
        Args:
            file_path: 文件路径
        """
        try:
            compressed_path = file_path.with_suffix('.pcap.gz')
            
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            # 计算压缩比
            original_size = file_path.stat().st_size
            compressed_size = compressed_path.stat().st_size
            compression_ratio = compressed_size / original_size if original_size > 0 else 0
            
            # 删除原文件
            file_path.unlink()
            
            app_logger.info(f"文件压缩完成: {compressed_path}, 压缩比: {compression_ratio:.2%}")
            self.stats['compression_ratio'] = compression_ratio
            
        except Exception as e:
            app_logger.error(f"压缩文件失败: {e}")
    
    def cleanup_old_files(self) -> None:
        """清理过期文件"""
        try:
            cutoff_time = datetime.now() - timedelta(days=self.retention_days)
            deleted_count = 0
            deleted_size = 0
            
            for file_path in self.base_path.glob("*.pcap*"):
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                
                if file_time < cutoff_time:
                    file_size = file_path.stat().st_size
                    file_path.unlink()
                    deleted_count += 1
                    deleted_size += file_size
                    app_logger.info(f"删除过期文件: {file_path}")
            
            if deleted_count > 0:
                app_logger.info(f"清理完成，删除 {deleted_count} 个文件，释放 {deleted_size / 1024 / 1024:.2f} MB")
            
        except Exception as e:
            app_logger.error(f"清理过期文件失败: {e}")
    
    def get_file_list(self) -> List[Dict[str, Any]]:
        """
        获取PCAP文件列表
        
        Returns:
            文件信息列表
        """
        files = []
        
        try:
            for file_path in sorted(self.base_path.glob("*.pcap*")):
                stat = file_path.stat()
                files.append({
                    'name': file_path.name,
                    'path': str(file_path),
                    'size': stat.st_size,
                    'size_mb': stat.st_size / 1024 / 1024,
                    'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'is_compressed': file_path.suffix == '.gz'
                })
        except Exception as e:
            app_logger.error(f"获取文件列表失败: {e}")
        
        return files
    
    def get_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        stats = self.stats.copy()
        stats.update({
            'is_writing': self.is_writing,
            'queue_size': self.write_queue.qsize(),
            'current_file': str(self.current_file_path) if self.current_file_path else None,
            'current_file_size': self.current_file_size,
            'current_file_size_mb': self.current_file_size / 1024 / 1024
        })
        return stats
    
    @staticmethod
    def _parse_size(size_str: str) -> int:
        """
        解析大小字符串
        
        Args:
            size_str: 大小字符串，如 "100MB"
            
        Returns:
            字节数
        """
        size_str = size_str.upper().strip()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)


# 全局PCAP存储器实例
pcap_storage = PcapStorage()
