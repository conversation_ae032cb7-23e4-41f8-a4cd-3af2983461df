2025-06-13 12:00:57 | INFO     | src.capture.packet_capture:__init__:38 | 数据包捕获器初始化完成
2025-06-13 12:00:57 | INFO     | src.capture.protocol_parser:__init__:36 | 协议解析器初始化完成
2025-06-13 12:00:57 | INFO     | src.storage.pcap_storage:__init__:52 | PCAP存储器初始化完成，存储路径: data\pcap
2025-06-13 12:01:23 | INFO     | src.capture.packet_capture:__init__:38 | 数据包捕获器初始化完成
2025-06-13 12:01:23 | INFO     | src.capture.protocol_parser:__init__:36 | 协议解析器初始化完成
2025-06-13 12:01:23 | INFO     | src.storage.pcap_storage:__init__:52 | PCAP存储器初始化完成，存储路径: data\pcap
2025-06-13 12:01:26 | INFO     | src.storage.database:create_tables:157 | 数据库表创建完成
2025-06-13 12:01:26 | INFO     | src.storage.database:__init__:151 | 数据库管理器初始化完成，数据库URL: sqlite:///./data/traffic_analysis.db
2025-06-13 12:01:26 | INFO     | src.detection.rule_engine:load_rules:79 | 规则加载完成，共 5 条规则
2025-06-13 12:01:26 | INFO     | src.detection.rule_engine:__init__:67 | 规则引擎初始化完成，加载 5 条规则
2025-06-13 12:01:26 | INFO     | __main__:test_logger:48 | 这是一条信息日志
2025-06-13 12:01:26 | WARNING  | __main__:test_logger:49 | 这是一条警告日志
2025-06-13 12:01:26 | ERROR    | __main__:test_logger:50 | 这是一条错误日志
2025-06-13 12:01:26 | ERROR    | src.capture.protocol_parser:parse_packet:74 | 解析数据包失败: unsupported operand type(s) for *: 'NoneType' and 'int'
2025-06-13 12:01:49 | INFO     | src.storage.database:create_tables:157 | 数据库表创建完成
2025-06-13 12:01:49 | INFO     | src.storage.database:__init__:151 | 数据库管理器初始化完成，数据库URL: sqlite:///./data/traffic_analysis.db
2025-06-13 12:01:50 | INFO     | src.capture.packet_capture:__init__:38 | 数据包捕获器初始化完成
2025-06-13 12:01:50 | INFO     | src.storage.pcap_storage:__init__:52 | PCAP存储器初始化完成，存储路径: data\pcap
2025-06-13 12:01:50 | INFO     | src.detection.rule_engine:load_rules:79 | 规则加载完成，共 5 条规则
2025-06-13 12:01:50 | INFO     | src.detection.rule_engine:__init__:67 | 规则引擎初始化完成，加载 5 条规则
2025-06-13 12:01:50 | INFO     | src.web.app:run_web_server:283 | 启动Web服务器: http://0.0.0.0:8080
2025-06-13 12:01:52 | INFO     | src.storage.database:create_tables:157 | 数据库表创建完成
2025-06-13 12:01:52 | INFO     | src.storage.database:__init__:151 | 数据库管理器初始化完成，数据库URL: sqlite:///./data/traffic_analysis.db
2025-06-13 12:01:53 | INFO     | src.capture.packet_capture:__init__:38 | 数据包捕获器初始化完成
2025-06-13 12:01:53 | INFO     | src.storage.pcap_storage:__init__:52 | PCAP存储器初始化完成，存储路径: data\pcap
2025-06-13 12:01:53 | INFO     | src.detection.rule_engine:load_rules:79 | 规则加载完成，共 5 条规则
2025-06-13 12:01:53 | INFO     | src.detection.rule_engine:__init__:67 | 规则引擎初始化完成，加载 5 条规则
2025-06-13 12:01:53 | INFO     | src.web.app:run_web_server:283 | 启动Web服务器: http://0.0.0.0:8080
2025-06-13 12:02:31 | INFO     | src.storage.database:create_tables:157 | 数据库表创建完成
2025-06-13 12:02:31 | INFO     | src.storage.database:__init__:151 | 数据库管理器初始化完成，数据库URL: sqlite:///./data/traffic_analysis.db
2025-06-13 12:02:52 | INFO     | src.storage.database:create_tables:157 | 数据库表创建完成
2025-06-13 12:02:52 | INFO     | src.storage.database:__init__:151 | 数据库管理器初始化完成，数据库URL: sqlite:///./data/traffic_analysis.db
2025-06-13 12:02:53 | INFO     | src.capture.packet_capture:__init__:38 | 数据包捕获器初始化完成
2025-06-13 12:02:53 | INFO     | src.storage.pcap_storage:__init__:52 | PCAP存储器初始化完成，存储路径: data\pcap
2025-06-13 12:02:53 | INFO     | src.detection.rule_engine:load_rules:79 | 规则加载完成，共 5 条规则
2025-06-13 12:02:53 | INFO     | src.detection.rule_engine:__init__:67 | 规则引擎初始化完成，加载 5 条规则
2025-06-13 12:02:53 | INFO     | src.web.app:run_web_server:283 | 启动Web服务器: http://0.0.0.0:8080
2025-06-13 12:03:46 | INFO     | src.storage.database:create_tables:157 | 数据库表创建完成
2025-06-13 12:03:46 | INFO     | src.storage.database:__init__:151 | 数据库管理器初始化完成，数据库URL: sqlite:///./data/traffic_analysis.db
