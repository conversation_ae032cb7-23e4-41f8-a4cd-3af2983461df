# 网络流量分析与安全检测系统

一个基于Python的网络流量分析与安全检测系统，支持实时流量捕获、协议解析、异常检测和安全事件管理。

## 功能特性

### 1. 流量采集与解析模块
- ✅ 支持全流量数据捕获（TCP、UDP、ICMP等网络层协议）
- ✅ 支持HTTP、DNS、FTP等应用层协议解析
- ✅ 支持多接口接入和BPF过滤规则
- ✅ 保留原始数据包完整信息

### 2. 异常与攻击检测模块
- ✅ 基于规则的异常流量检测
- ✅ 内置常见攻击检测规则（DDoS、SQL注入、端口扫描等）
- ✅ 支持自定义检测规则
- ✅ 实时安全事件生成和告警

### 3. 原始流量存储模块
- ✅ PCAP/PCAPNG标准格式存储
- ✅ 支持文件压缩和自动归档
- ✅ 可配置的存储策略和保留周期
- ✅ 数据库存储流量元数据

### 4. 管理与可视化模块
- ✅ Web管理界面
- ✅ 实时流量监控图表
- ✅ 安全事件统计和查询
- ✅ 系统配置管理

## 系统架构

```
├── src/
│   ├── core/           # 核心模块
│   │   ├── config.py   # 配置管理
│   │   └── logger.py   # 日志管理
│   ├── capture/        # 流量采集模块
│   │   ├── packet_capture.py    # 数据包捕获
│   │   └── protocol_parser.py   # 协议解析
│   ├── storage/        # 存储模块
│   │   ├── pcap_storage.py     # PCAP文件存储
│   │   └── database.py         # 数据库管理
│   ├── detection/      # 检测模块
│   │   └── rule_engine.py      # 规则引擎
│   └── web/           # Web界面
│       ├── app.py     # Flask应用
│       └── templates/ # HTML模板
├── config.yaml        # 系统配置文件
├── main.py            # 主程序
├── run_web.py         # Web界面启动脚本
└── requirements.txt   # 依赖包列表
```

## 安装和部署

### 1. 环境要求
- Python 3.8+
- Windows/Linux/macOS
- 管理员权限（用于网络数据包捕获）

### 2. 安装依赖
```bash
# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 安装依赖包
pip install -r requirements.txt
```

### 3. 配置系统
编辑 `config.yaml` 文件，配置网络接口、存储路径等参数：

```yaml
network:
  interfaces:
    - "eth0"        # 修改为实际的网络接口名
  bpf_filter: ""    # 可选的BPF过滤规则

storage:
  pcap:
    base_path: "./data/pcap"
    retention_days: 30

web:
  host: "0.0.0.0"
  port: 8080
```

### 4. 启动系统

#### 方式一：完整系统启动
```bash
# 需要管理员权限
sudo python main.py
```

#### 方式二：仅启动Web界面
```bash
# 用于查看已有数据，不需要管理员权限
python run_web.py
```

### 5. 访问Web界面
打开浏览器访问：http://localhost:8080

## 使用说明

### 1. 系统监控
- **仪表板**：查看系统运行状态、流量统计、最近告警
- **流量监控**：实时流量图表、协议分布、捕获控制
- **安全事件**：查看检测到的安全事件，支持按严重程度筛选

### 2. 规则管理
- 查看所有检测规则的状态和统计信息
- 启用/禁用特定规则
- 查看规则匹配次数

### 3. 文件管理
- 查看生成的PCAP文件列表
- 文件大小、创建时间、压缩状态等信息

### 4. 系统配置
- 查看当前系统配置
- 监控系统运行参数

## 内置检测规则

系统内置了以下安全检测规则：

1. **SYN洪水攻击检测** - 检测TCP SYN洪水攻击
2. **端口扫描检测** - 检测端口扫描行为
3. **DNS隧道检测** - 检测DNS隧道通信
4. **SQL注入检测** - 检测HTTP请求中的SQL注入攻击
5. **异常大数据包检测** - 检测异常大的数据包

## 自定义规则

可以在 `rules/` 目录下创建JSON格式的自定义规则文件：

```json
{
  "id": "custom_rule_1",
  "name": "自定义规则",
  "description": "检测特定攻击模式",
  "severity": "high",
  "category": "custom",
  "enabled": true,
  "conditions": [
    {"type": "ip_match", "field": "src_ip", "value": "*************"},
    {"type": "port_match", "field": "dst_port", "value": 80}
  ],
  "actions": ["alert", "log"],
  "metadata": {"attack_type": "custom_attack"}
}
```

## 性能优化

### 1. 数据包捕获优化
- 使用BPF过滤规则减少不必要的数据包捕获
- 调整缓冲区大小和超时参数
- 在高流量环境中考虑使用专用网络接口

### 2. 存储优化
- 启用PCAP文件压缩
- 合理设置文件大小限制和保留周期
- 使用SSD存储提高I/O性能

### 3. 检测优化
- 禁用不需要的检测规则
- 调整频率检测的阈值和时间窗口
- 定期清理过期的统计数据

## 故障排除

### 1. 权限问题
```bash
# Linux/macOS需要root权限
sudo python main.py

# 或者设置网络接口权限
sudo setcap cap_net_raw,cap_net_admin=eip /usr/bin/python3
```

### 2. 网络接口问题
```bash
# 查看可用网络接口
python -c "from scapy.all import get_if_list; print(get_if_list())"
```

### 3. 依赖包问题
```bash
# 更新pip
pip install --upgrade pip

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

### 4. 日志查看
系统日志保存在 `logs/` 目录下：
- `app.log` - 应用日志
- `error.log` - 错误日志
- `security.log` - 安全事件日志

## 扩展开发

### 1. 添加新的协议解析器
在 `src/capture/protocol_parser.py` 中添加新的解析函数。

### 2. 添加新的检测规则
在 `src/detection/rule_engine.py` 中添加新的条件检查函数。

### 3. 添加新的存储后端
实现新的存储类，继承基础存储接口。

### 4. 扩展Web界面
在 `src/web/` 目录下添加新的路由和模板。

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进本项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者
