{% extends "base.html" %}

{% block content %}
<!-- 仪表板部分 -->
<div id="dashboard-section" class="content-section">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-tachometer-alt me-2"></i>系统仪表板</h2>
        </div>
    </div>
    
    <!-- 状态卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card status-card success">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">数据包捕获</h6>
                            <h4 id="packet-count">0</h4>
                            <small class="text-muted">总数据包</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-network-wired fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card status-card warning">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">安全事件</h6>
                            <h4 id="security-events-count">0</h4>
                            <small class="text-muted">今日事件</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shield-alt fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card status-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">检测规则</h6>
                            <h4 id="active-rules-count">0</h4>
                            <small class="text-muted">活跃规则</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-cogs fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card status-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">存储使用</h6>
                            <h4 id="storage-usage">0 MB</h4>
                            <small class="text-muted">PCAP文件</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-hdd fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line me-2"></i>流量趋势</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="traffic-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie me-2"></i>协议分布</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="protocol-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 最近告警 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>最近告警</h5>
                </div>
                <div class="card-body">
                    <div id="recent-alerts">
                        <div class="text-center text-muted">
                            <i class="fas fa-spinner fa-spin"></i> 加载中...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 流量监控部分 -->
<div id="traffic-section" class="content-section" style="display: none;">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-network-wired me-2"></i>流量监控</h2>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>流量统计</h5>
                </div>
                <div class="card-body" id="traffic-stats">
                    加载中...
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>捕获控制</h5>
                </div>
                <div class="card-body">
                    <button class="btn btn-success me-2" onclick="startCapture()">
                        <i class="fas fa-play"></i> 开始捕获
                    </button>
                    <button class="btn btn-danger" onclick="stopCapture()">
                        <i class="fas fa-stop"></i> 停止捕获
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 安全事件部分 -->
<div id="security-section" class="content-section" style="display: none;">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-shield-alt me-2"></i>安全事件</h2>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <div class="row">
                <div class="col-md-6">
                    <h5>事件列表</h5>
                </div>
                <div class="col-md-6 text-end">
                    <select class="form-select d-inline-block w-auto" id="severity-filter" onchange="loadSecurityEvents()">
                        <option value="">所有严重程度</option>
                        <option value="low">低</option>
                        <option value="medium">中</option>
                        <option value="high">高</option>
                        <option value="critical">严重</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div id="security-events-table">
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 检测规则部分 -->
<div id="rules-section" class="content-section" style="display: none;">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-cogs me-2"></i>检测规则</h2>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h5>规则管理</h5>
        </div>
        <div class="card-body">
            <div id="rules-table">
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- PCAP文件部分 -->
<div id="files-section" class="content-section" style="display: none;">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-file-archive me-2"></i>PCAP文件</h2>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h5>文件列表</h5>
        </div>
        <div class="card-body">
            <div id="pcap-files-table">
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 系统设置部分 -->
<div id="settings-section" class="content-section" style="display: none;">
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-cog me-2"></i>系统设置</h2>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h5>配置信息</h5>
        </div>
        <div class="card-body">
            <div id="config-display">
                <div class="text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i> 加载中...
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let trafficChart, protocolChart;
    
    // 加载仪表板数据
    async function loadDashboard() {
        const status = await apiCall('status');
        if (!status) return;
        
        // 更新状态卡片
        document.getElementById('packet-count').textContent = status.capture.total_packets || 0;
        document.getElementById('active-rules-count').textContent = status.detection.enabled_rules || 0;
        
        // 加载最近告警
        loadRecentAlerts();
        
        // 更新图表
        updateCharts();
    }
    
    // 加载最近告警
    async function loadRecentAlerts() {
        const alerts = await apiCall('alerts/recent');
        if (!alerts) return;
        
        const container = document.getElementById('recent-alerts');
        
        if (alerts.alerts.length === 0) {
            container.innerHTML = '<div class="text-muted">暂无告警</div>';
            return;
        }
        
        let html = '<div class="table-responsive"><table class="table table-sm">';
        html += '<thead><tr><th>时间</th><th>事件</th><th>严重程度</th><th>源IP</th></tr></thead><tbody>';
        
        alerts.alerts.forEach(alert => {
            const severityClass = {
                'low': 'secondary',
                'medium': 'warning',
                'high': 'danger',
                'critical': 'dark'
            }[alert.severity] || 'secondary';
            
            html += `<tr>
                <td>${formatTime(alert.timestamp)}</td>
                <td>${alert.title}</td>
                <td><span class="badge bg-${severityClass}">${alert.severity}</span></td>
                <td>${alert.src_ip || '-'}</td>
            </tr>`;
        });
        
        html += '</tbody></table></div>';
        container.innerHTML = html;
    }
    
    // 更新图表
    function updateCharts() {
        // 流量趋势图
        if (!trafficChart) {
            const ctx = document.getElementById('traffic-chart').getContext('2d');
            trafficChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '数据包/秒',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
        
        // 协议分布图
        if (!protocolChart) {
            const ctx = document.getElementById('protocol-chart').getContext('2d');
            protocolChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['TCP', 'UDP', 'ICMP', 'Other'],
                    datasets: [{
                        data: [60, 30, 5, 5],
                        backgroundColor: [
                            'rgb(255, 99, 132)',
                            'rgb(54, 162, 235)',
                            'rgb(255, 205, 86)',
                            'rgb(75, 192, 192)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
    }
    
    // 加载流量数据
    async function loadTrafficData() {
        const stats = await apiCall('traffic-stats');
        if (!stats) return;
        
        const container = document.getElementById('traffic-stats');
        let html = `
            <p><strong>数据包总数:</strong> ${stats.packet_count}</p>
            <p><strong>时间范围:</strong> ${formatTime(stats.time_range.start)} - ${formatTime(stats.time_range.end)}</p>
            <h6>协议分布:</h6>
            <ul>
        `;
        
        for (const [protocol, percentage] of Object.entries(stats.protocol_distribution)) {
            html += `<li>${protocol}: ${percentage}%</li>`;
        }
        
        html += '</ul>';
        container.innerHTML = html;
    }
    
    // 加载安全事件
    async function loadSecurityEvents() {
        const severity = document.getElementById('severity-filter')?.value || '';
        const events = await apiCall(`security-events?severity=${severity}`);
        if (!events) return;
        
        const container = document.getElementById('security-events-table');
        
        if (events.events.length === 0) {
            container.innerHTML = '<div class="text-muted">暂无安全事件</div>';
            return;
        }
        
        let html = '<div class="table-responsive"><table class="table">';
        html += '<thead><tr><th>时间</th><th>事件类型</th><th>严重程度</th><th>源IP</th><th>目标IP</th><th>描述</th></tr></thead><tbody>';
        
        events.events.forEach(event => {
            const severityClass = {
                'low': 'secondary',
                'medium': 'warning',
                'high': 'danger',
                'critical': 'dark'
            }[event.severity] || 'secondary';
            
            html += `<tr>
                <td>${formatTime(event.created_at)}</td>
                <td>${event.event_type}</td>
                <td><span class="badge bg-${severityClass}">${event.severity}</span></td>
                <td>${event.src_ip || '-'}</td>
                <td>${event.dst_ip || '-'}</td>
                <td>${event.description}</td>
            </tr>`;
        });
        
        html += '</tbody></table></div>';
        container.innerHTML = html;
    }
    
    // 加载检测规则
    async function loadRules() {
        const rules = await apiCall('rules');
        if (!rules) return;
        
        const container = document.getElementById('rules-table');
        
        let html = '<div class="table-responsive"><table class="table">';
        html += '<thead><tr><th>规则名称</th><th>类别</th><th>严重程度</th><th>状态</th><th>匹配次数</th><th>操作</th></tr></thead><tbody>';
        
        rules.rules.forEach(rule => {
            const statusClass = rule.enabled ? 'success' : 'secondary';
            const statusText = rule.enabled ? '启用' : '禁用';
            
            html += `<tr>
                <td>${rule.name}</td>
                <td>${rule.category}</td>
                <td>${rule.severity}</td>
                <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                <td>${rule.stats.matches || 0}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="toggleRule('${rule.id}')">
                        ${rule.enabled ? '禁用' : '启用'}
                    </button>
                </td>
            </tr>`;
        });
        
        html += '</tbody></table></div>';
        container.innerHTML = html;
    }
    
    // 加载PCAP文件
    async function loadPcapFiles() {
        const files = await apiCall('pcap-files');
        if (!files) return;
        
        const container = document.getElementById('pcap-files-table');
        
        if (files.files.length === 0) {
            container.innerHTML = '<div class="text-muted">暂无PCAP文件</div>';
            return;
        }
        
        let html = '<div class="table-responsive"><table class="table">';
        html += '<thead><tr><th>文件名</th><th>大小</th><th>创建时间</th><th>压缩</th></tr></thead><tbody>';
        
        files.files.forEach(file => {
            html += `<tr>
                <td>${file.name}</td>
                <td>${formatFileSize(file.size)}</td>
                <td>${formatTime(file.created_time)}</td>
                <td>${file.is_compressed ? '是' : '否'}</td>
            </tr>`;
        });
        
        html += '</tbody></table></div>';
        container.innerHTML = html;
    }
    
    // 加载系统设置
    async function loadSettings() {
        const config = await apiCall('config');
        if (!config) return;
        
        const container = document.getElementById('config-display');
        container.innerHTML = `<pre>${JSON.stringify(config, null, 2)}</pre>`;
    }
    
    // 切换规则状态
    async function toggleRule(ruleId) {
        try {
            const response = await fetch(`/api/rules/${ruleId}/toggle`, {
                method: 'POST'
            });
            const result = await response.json();
            
            if (response.ok) {
                showAlert(result.message, 'success');
                loadRules();
            } else {
                showAlert(result.error, 'danger');
            }
        } catch (error) {
            showAlert('操作失败: ' + error.message, 'danger');
        }
    }
    
    // 开始捕获
    async function startCapture() {
        try {
            const response = await fetch('/api/capture/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            });
            const result = await response.json();
            
            if (response.ok) {
                showAlert(result.message, 'success');
            } else {
                showAlert(result.error, 'danger');
            }
        } catch (error) {
            showAlert('操作失败: ' + error.message, 'danger');
        }
    }
    
    // 停止捕获
    async function stopCapture() {
        try {
            const response = await fetch('/api/capture/stop', {
                method: 'POST'
            });
            const result = await response.json();
            
            if (response.ok) {
                showAlert(result.message, 'success');
            } else {
                showAlert(result.error, 'danger');
            }
        } catch (error) {
            showAlert('操作失败: ' + error.message, 'danger');
        }
    }
</script>
{% endblock %}
