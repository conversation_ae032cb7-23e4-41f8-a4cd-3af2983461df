#!/usr/bin/env python3
"""
网络流量分析与安全检测系统主程序
Network Traffic Analysis and Security Detection System
"""

import sys
import signal
import threading
import time
from typing import Dict, Any

from src.core.config import config
from src.core.logger import app_logger
from src.capture.packet_capture import packet_capture
from src.capture.protocol_parser import protocol_parser
from src.storage.pcap_storage import pcap_storage
from src.storage.database import db_manager
from src.detection.rule_engine import rule_engine


class TrafficAnalysisSystem:
    """流量分析系统主类"""
    
    def __init__(self):
        """初始化系统"""
        self.is_running = False
        self.stats_thread: threading.Thread = None
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        app_logger.info("流量分析系统初始化完成")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        app_logger.info(f"接收到信号 {signum}，正在关闭系统...")
        self.stop()
    
    def start(self):
        """启动系统"""
        if self.is_running:
            app_logger.warning("系统已在运行中")
            return
        
        app_logger.info("正在启动网络流量分析与安全检测系统...")
        
        try:
            # 启动PCAP存储
            pcap_storage.start_writing()
            
            # 注册数据包处理器
            packet_capture.add_packet_handler(self._packet_handler)
            
            # 启动数据包捕获
            interface = config.get('network.interfaces', ['any'])[0]
            bpf_filter = config.get('network.bpf_filter', '')
            packet_capture.start_capture(interface=interface, bpf_filter=bpf_filter)
            
            # 启动统计线程
            self.stats_thread = threading.Thread(target=self._stats_worker, daemon=True)
            self.stats_thread.start()
            
            self.is_running = True
            app_logger.info("系统启动成功")
            
            # 主循环
            self._main_loop()
            
        except Exception as e:
            app_logger.error(f"系统启动失败: {e}")
            self.stop()
    
    def stop(self):
        """停止系统"""
        if not self.is_running:
            return
        
        app_logger.info("正在停止系统...")
        self.is_running = False
        
        try:
            # 停止数据包捕获
            packet_capture.stop_capture()
            
            # 停止PCAP存储
            pcap_storage.stop_writing()
            
            app_logger.info("系统已停止")
            
        except Exception as e:
            app_logger.error(f"停止系统时发生错误: {e}")
    
    def _packet_handler(self, packet):
        """
        数据包处理器
        
        Args:
            packet: 捕获的数据包
        """
        try:
            # 解析数据包
            packet_data = protocol_parser.parse_packet(packet)
            
            # 保存到PCAP文件
            pcap_storage.write_packet(packet)
            
            # 保存到数据库（可选，避免数据库过载）
            if self._should_save_to_db(packet_data):
                db_manager.save_packet_record(packet_data)
            
            # 安全检测
            detection_results = rule_engine.check_packet(packet_data)
            
            # 处理检测结果
            for result in detection_results:
                self._handle_detection_result(result)
        
        except Exception as e:
            app_logger.error(f"处理数据包失败: {e}")
    
    def _should_save_to_db(self, packet_data: Dict[str, Any]) -> bool:
        """
        判断是否应该保存到数据库
        
        Args:
            packet_data: 数据包数据
            
        Returns:
            是否保存
        """
        # 只保存重要的数据包到数据库，避免数据库过载
        # 例如：HTTP请求、DNS查询、安全事件相关的包
        
        protocols = packet_data.get('protocols', [])
        
        # 保存应用层协议数据包
        if any(proto in protocols for proto in ['http', 'dns', 'ftp', 'smtp']):
            return True
        
        # 保存异常端口的数据包
        dst_port = packet_data.get('dst_port')
        if dst_port and dst_port not in [80, 443, 53, 22, 21, 25]:
            return True
        
        # 随机采样保存其他数据包（1%）
        import random
        return random.random() < 0.01
    
    def _handle_detection_result(self, result: Dict[str, Any]) -> None:
        """
        处理检测结果
        
        Args:
            result: 检测结果
        """
        try:
            # 生成事件ID
            import hashlib
            event_data = {
                'event_id': hashlib.md5(
                    f"{result['rule_id']}_{result['timestamp']}_{result['packet_data'].get('src_ip')}".encode()
                ).hexdigest(),
                'event_type': result['category'],
                'severity': result['severity'],
                'title': result['rule_name'],
                'description': result['description'],
                'src_ip': result['packet_data'].get('src_ip'),
                'dst_ip': result['packet_data'].get('dst_ip'),
                'src_port': result['packet_data'].get('src_port'),
                'dst_port': result['packet_data'].get('dst_port'),
                'protocol': result['packet_data'].get('protocol'),
                'attack_type': result['metadata'].get('attack_type'),
                'confidence': 0.8,  # 默认置信度
                'impact_score': self._calculate_impact_score(result),
                'related_packets': [result['packet_data']],
                'first_seen': result['timestamp'],
                'last_seen': result['timestamp']
            }
            
            # 保存安全事件
            db_manager.save_security_event(event_data)
            
            # 执行响应动作
            self._execute_actions(result['actions'], result)
            
        except Exception as e:
            app_logger.error(f"处理检测结果失败: {e}")
    
    def _calculate_impact_score(self, result: Dict[str, Any]) -> float:
        """
        计算影响分数
        
        Args:
            result: 检测结果
            
        Returns:
            影响分数 (0.0-1.0)
        """
        severity_scores = {
            'low': 0.2,
            'medium': 0.5,
            'high': 0.8,
            'critical': 1.0
        }
        
        return severity_scores.get(result['severity'], 0.5)
    
    def _execute_actions(self, actions: list, result: Dict[str, Any]) -> None:
        """
        执行响应动作
        
        Args:
            actions: 动作列表
            result: 检测结果
        """
        for action in actions:
            try:
                if action == 'alert':
                    self._send_alert(result)
                elif action == 'log':
                    self._log_event(result)
                elif action == 'block':
                    self._block_traffic(result)
            except Exception as e:
                app_logger.error(f"执行动作 {action} 失败: {e}")
    
    def _send_alert(self, result: Dict[str, Any]) -> None:
        """发送告警"""
        # 这里可以实现邮件、短信、Webhook等告警方式
        app_logger.warning(f"安全告警: {result['rule_name']} - {result['packet_data'].get('src_ip')}")
    
    def _log_event(self, result: Dict[str, Any]) -> None:
        """记录事件"""
        app_logger.info(f"安全事件: {result['rule_name']} - {result['description']}")
    
    def _block_traffic(self, result: Dict[str, Any]) -> None:
        """阻断流量"""
        # 这里可以实现防火墙规则添加等阻断措施
        app_logger.warning(f"阻断流量: {result['packet_data'].get('src_ip')}")
    
    def _main_loop(self):
        """主循环"""
        try:
            while self.is_running:
                time.sleep(1)
                
                # 定期清理过期文件
                if int(time.time()) % 3600 == 0:  # 每小时执行一次
                    pcap_storage.cleanup_old_files()
        
        except KeyboardInterrupt:
            app_logger.info("接收到中断信号")
        except Exception as e:
            app_logger.error(f"主循环异常: {e}")
    
    def _stats_worker(self):
        """统计工作线程"""
        while self.is_running:
            try:
                # 收集统计信息
                capture_stats = packet_capture.get_stats()
                storage_stats = pcap_storage.get_stats()
                rule_stats = rule_engine.get_rule_stats()
                
                # 保存系统指标
                db_manager.save_system_metric('packets_per_second', capture_stats.get('packets_per_second', 0), 'pps')
                db_manager.save_system_metric('total_packets', capture_stats.get('total_packets', 0), 'count')
                db_manager.save_system_metric('pcap_files_created', storage_stats.get('files_created', 0), 'count')
                
                # 输出统计信息
                if capture_stats.get('total_packets', 0) > 0:
                    app_logger.info(
                        f"统计信息 - 总包数: {capture_stats['total_packets']}, "
                        f"速率: {capture_stats.get('packets_per_second', 0):.2f} pps, "
                        f"规则匹配: {sum(s['matches'] for s in rule_stats['rule_stats'].values())}"
                    )
                
                time.sleep(30)  # 每30秒更新一次统计
                
            except Exception as e:
                app_logger.error(f"统计线程异常: {e}")
                time.sleep(30)
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        return {
            'is_running': self.is_running,
            'capture_stats': packet_capture.get_stats(),
            'storage_stats': pcap_storage.get_stats(),
            'rule_stats': rule_engine.get_rule_stats(),
            'config': {
                'interfaces': config.get('network.interfaces'),
                'storage_path': config.get('storage.pcap.base_path'),
                'retention_days': config.get('storage.pcap.retention_days')
            }
        }


def main():
    """主函数"""
    app_logger.info("启动网络流量分析与安全检测系统")
    
    # 检查运行权限
    if sys.platform != 'win32' and os.geteuid() != 0:
        app_logger.error("需要root权限运行数据包捕获功能")
        sys.exit(1)
    
    # 创建并启动系统
    system = TrafficAnalysisSystem()
    
    try:
        system.start()
    except KeyboardInterrupt:
        app_logger.info("用户中断")
    except Exception as e:
        app_logger.error(f"系统运行异常: {e}")
    finally:
        system.stop()


if __name__ == "__main__":
    import os
    main()
