"""
数据库模型和管理模块
用于存储流量元数据和检测结果
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Float, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session

from ..core.config import config
from ..core.logger import app_logger

Base = declarative_base()


class TrafficSession(Base):
    """流量会话表"""
    __tablename__ = 'traffic_sessions'
    
    id = Column(Integer, primary_key=True)
    session_id = Column(String(64), unique=True, index=True)  # 会话唯一标识
    src_ip = Column(String(45), index=True)  # 源IP地址
    dst_ip = Column(String(45), index=True)  # 目标IP地址
    src_port = Column(Integer, index=True)   # 源端口
    dst_port = Column(Integer, index=True)   # 目标端口
    protocol = Column(String(10), index=True)  # 协议类型
    start_time = Column(DateTime, index=True)  # 会话开始时间
    end_time = Column(DateTime)                # 会话结束时间
    packet_count = Column(Integer, default=0)  # 数据包数量
    byte_count = Column(Integer, default=0)    # 字节数
    duration = Column(Float, default=0.0)      # 持续时间（秒）
    status = Column(String(20), default='active')  # 会话状态
    created_at = Column(DateTime, default=datetime.utcnow)


class PacketRecord(Base):
    """数据包记录表"""
    __tablename__ = 'packet_records'
    
    id = Column(Integer, primary_key=True)
    session_id = Column(String(64), index=True)  # 关联会话ID
    timestamp = Column(DateTime, index=True)      # 时间戳
    src_ip = Column(String(45), index=True)
    dst_ip = Column(String(45), index=True)
    src_port = Column(Integer)
    dst_port = Column(Integer)
    protocol = Column(String(10), index=True)
    packet_size = Column(Integer)
    application_protocol = Column(String(20))  # 应用层协议
    flags = Column(String(20))                 # TCP标志位
    payload_size = Column(Integer)
    pcap_file = Column(String(255))           # 对应的PCAP文件
    packet_offset = Column(Integer)           # 在PCAP文件中的偏移
    parsed_data = Column(JSON)                # 解析后的数据
    created_at = Column(DateTime, default=datetime.utcnow)


class SecurityEvent(Base):
    """安全事件表"""
    __tablename__ = 'security_events'
    
    id = Column(Integer, primary_key=True)
    event_id = Column(String(64), unique=True, index=True)
    event_type = Column(String(50), index=True)    # 事件类型
    severity = Column(String(20), index=True)      # 严重程度
    title = Column(String(255))                    # 事件标题
    description = Column(Text)                     # 事件描述
    src_ip = Column(String(45), index=True)
    dst_ip = Column(String(45), index=True)
    src_port = Column(Integer)
    dst_port = Column(Integer)
    protocol = Column(String(10))
    attack_type = Column(String(50), index=True)   # 攻击类型
    confidence = Column(Float)                     # 置信度
    impact_score = Column(Float)                   # 影响分数
    status = Column(String(20), default='new')     # 处理状态
    false_positive = Column(Boolean, default=False)
    related_packets = Column(JSON)                 # 相关数据包
    mitigation_actions = Column(JSON)              # 缓解措施
    analyst_notes = Column(Text)                   # 分析师备注
    first_seen = Column(DateTime, index=True)
    last_seen = Column(DateTime)
    occurrence_count = Column(Integer, default=1)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class ThreatIntelligence(Base):
    """威胁情报表"""
    __tablename__ = 'threat_intelligence'
    
    id = Column(Integer, primary_key=True)
    indicator = Column(String(255), unique=True, index=True)  # 威胁指标
    indicator_type = Column(String(20), index=True)           # 指标类型（IP、域名、哈希等）
    threat_type = Column(String(50), index=True)              # 威胁类型
    malware_family = Column(String(100))                      # 恶意软件家族
    confidence = Column(Float)                                # 置信度
    severity = Column(String(20))                             # 严重程度
    source = Column(String(100))                              # 情报来源
    description = Column(Text)                                # 描述
    tags = Column(JSON)                                       # 标签
    first_seen = Column(DateTime)
    last_seen = Column(DateTime)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class SystemMetrics(Base):
    """系统指标表"""
    __tablename__ = 'system_metrics'
    
    id = Column(Integer, primary_key=True)
    metric_name = Column(String(100), index=True)
    metric_value = Column(Float)
    metric_unit = Column(String(20))
    timestamp = Column(DateTime, index=True, default=datetime.utcnow)
    tags = Column(JSON)  # 额外的标签信息


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.database_url = config.get('storage.database.url', 'sqlite:///./data/traffic_analysis.db')
        self.engine = create_engine(
            self.database_url,
            pool_size=config.get('storage.database.pool_size', 10),
            max_overflow=config.get('storage.database.max_overflow', 20),
            echo=config.get('system.debug', False)
        )
        
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # 创建表
        self.create_tables()
        
        app_logger.info(f"数据库管理器初始化完成，数据库URL: {self.database_url}")
    
    def create_tables(self) -> None:
        """创建数据库表"""
        try:
            Base.metadata.create_all(bind=self.engine)
            app_logger.info("数据库表创建完成")
        except Exception as e:
            app_logger.error(f"创建数据库表失败: {e}")
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        return self.SessionLocal()
    
    def save_packet_record(self, packet_data: Dict[str, Any]) -> Optional[int]:
        """
        保存数据包记录
        
        Args:
            packet_data: 数据包数据
            
        Returns:
            记录ID
        """
        try:
            with self.get_session() as session:
                record = PacketRecord(
                    session_id=packet_data.get('session_id'),
                    timestamp=datetime.fromisoformat(packet_data.get('timestamp')),
                    src_ip=packet_data.get('src_ip'),
                    dst_ip=packet_data.get('dst_ip'),
                    src_port=packet_data.get('src_port'),
                    dst_port=packet_data.get('dst_port'),
                    protocol=packet_data.get('protocol'),
                    packet_size=packet_data.get('packet_size'),
                    application_protocol=packet_data.get('application_protocol'),
                    payload_size=packet_data.get('payload_size', 0),
                    parsed_data=packet_data
                )
                
                session.add(record)
                session.commit()
                return record.id
                
        except Exception as e:
            app_logger.error(f"保存数据包记录失败: {e}")
            return None
    
    def save_security_event(self, event_data: Dict[str, Any]) -> Optional[int]:
        """
        保存安全事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            事件ID
        """
        try:
            with self.get_session() as session:
                event = SecurityEvent(
                    event_id=event_data.get('event_id'),
                    event_type=event_data.get('event_type'),
                    severity=event_data.get('severity'),
                    title=event_data.get('title'),
                    description=event_data.get('description'),
                    src_ip=event_data.get('src_ip'),
                    dst_ip=event_data.get('dst_ip'),
                    src_port=event_data.get('src_port'),
                    dst_port=event_data.get('dst_port'),
                    protocol=event_data.get('protocol'),
                    attack_type=event_data.get('attack_type'),
                    confidence=event_data.get('confidence'),
                    impact_score=event_data.get('impact_score'),
                    related_packets=event_data.get('related_packets'),
                    first_seen=datetime.fromisoformat(event_data.get('first_seen')),
                    last_seen=datetime.fromisoformat(event_data.get('last_seen', event_data.get('first_seen')))
                )
                
                session.add(event)
                session.commit()
                return event.id
                
        except Exception as e:
            app_logger.error(f"保存安全事件失败: {e}")
            return None
    
    def get_security_events(self, limit: int = 100, offset: int = 0, 
                           severity: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取安全事件列表
        
        Args:
            limit: 限制数量
            offset: 偏移量
            severity: 严重程度过滤
            
        Returns:
            事件列表
        """
        try:
            with self.get_session() as session:
                query = session.query(SecurityEvent)
                
                if severity:
                    query = query.filter(SecurityEvent.severity == severity)
                
                events = query.order_by(SecurityEvent.created_at.desc()).offset(offset).limit(limit).all()
                
                return [
                    {
                        'id': event.id,
                        'event_id': event.event_id,
                        'event_type': event.event_type,
                        'severity': event.severity,
                        'title': event.title,
                        'description': event.description,
                        'src_ip': event.src_ip,
                        'dst_ip': event.dst_ip,
                        'attack_type': event.attack_type,
                        'confidence': event.confidence,
                        'status': event.status,
                        'first_seen': event.first_seen.isoformat() if event.first_seen else None,
                        'created_at': event.created_at.isoformat()
                    }
                    for event in events
                ]
                
        except Exception as e:
            app_logger.error(f"获取安全事件失败: {e}")
            return []
    
    def save_system_metric(self, metric_name: str, metric_value: float, 
                          metric_unit: str = '', tags: Optional[Dict] = None) -> None:
        """
        保存系统指标
        
        Args:
            metric_name: 指标名称
            metric_value: 指标值
            metric_unit: 指标单位
            tags: 标签
        """
        try:
            with self.get_session() as session:
                metric = SystemMetrics(
                    metric_name=metric_name,
                    metric_value=metric_value,
                    metric_unit=metric_unit,
                    tags=tags or {}
                )
                
                session.add(metric)
                session.commit()
                
        except Exception as e:
            app_logger.error(f"保存系统指标失败: {e}")


# 全局数据库管理器实例
db_manager = DatabaseManager()
