"""
协议解析模块
解析各种网络协议的数据包内容
"""

import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from scapy.all import Packet
from scapy.layers.inet import IP, TCP, UDP, ICMP
from scapy.layers.inet6 import IPv6
from scapy.layers.l2 import Ether, ARP
from scapy.layers.dns import DNS, DNSQR, DNSRR
from scapy.layers.http import HTTP, HTTPRequest, HTTPResponse

from ..core.logger import app_logger


class ProtocolParser:
    """协议解析器"""
    
    def __init__(self):
        """初始化协议解析器"""
        self.supported_protocols = {
            'ethernet': self._parse_ethernet,
            'ip': self._parse_ip,
            'ipv6': self._parse_ipv6,
            'tcp': self._parse_tcp,
            'udp': self._parse_udp,
            'icmp': self._parse_icmp,
            'arp': self._parse_arp,
            'dns': self._parse_dns,
            'http': self._parse_http
        }
        
        app_logger.info("协议解析器初始化完成")
    
    def parse_packet(self, packet: Packet) -> Dict[str, Any]:
        """
        解析数据包
        
        Args:
            packet: 要解析的数据包
            
        Returns:
            解析结果字典
        """
        result = {
            'timestamp': datetime.now().isoformat(),
            'packet_size': len(packet),
            'protocols': [],
            'layers': {},
            'summary': str(packet.summary()),
            'raw_data': bytes(packet).hex()
        }
        
        try:
            # 解析各层协议
            for layer in packet.layers():
                protocol_name = layer.__name__.lower()
                
                if protocol_name in self.supported_protocols:
                    parser_func = self.supported_protocols[protocol_name]
                    layer_data = parser_func(packet[layer])
                    
                    if layer_data:
                        result['protocols'].append(protocol_name)
                        result['layers'][protocol_name] = layer_data
            
            # 提取关键信息到顶层
            self._extract_key_info(result)
            
        except Exception as e:
            app_logger.error(f"解析数据包失败: {e}")
            result['error'] = str(e)
        
        return result
    
    def _parse_ethernet(self, layer: Ether) -> Dict[str, Any]:
        """解析以太网层"""
        return {
            'src_mac': layer.src,
            'dst_mac': layer.dst,
            'type': layer.type,
            'type_name': self._get_ether_type_name(layer.type)
        }
    
    def _parse_ip(self, layer: IP) -> Dict[str, Any]:
        """解析IP层"""
        return {
            'version': layer.version,
            'header_length': layer.ihl * 4,
            'tos': layer.tos,
            'total_length': layer.len,
            'identification': layer.id,
            'flags': {
                'dont_fragment': bool(layer.flags & 2),
                'more_fragments': bool(layer.flags & 1)
            },
            'fragment_offset': layer.frag,
            'ttl': layer.ttl,
            'protocol': layer.proto,
            'protocol_name': self._get_ip_protocol_name(layer.proto),
            'checksum': layer.chksum,
            'src_ip': layer.src,
            'dst_ip': layer.dst,
            'options': layer.options if hasattr(layer, 'options') else []
        }
    
    def _parse_ipv6(self, layer: IPv6) -> Dict[str, Any]:
        """解析IPv6层"""
        return {
            'version': layer.version,
            'traffic_class': layer.tc,
            'flow_label': layer.fl,
            'payload_length': layer.plen,
            'next_header': layer.nh,
            'hop_limit': layer.hlim,
            'src_ip': layer.src,
            'dst_ip': layer.dst
        }
    
    def _parse_tcp(self, layer: TCP) -> Dict[str, Any]:
        """解析TCP层"""
        flags = {
            'fin': bool(layer.flags & 1),
            'syn': bool(layer.flags & 2),
            'rst': bool(layer.flags & 4),
            'psh': bool(layer.flags & 8),
            'ack': bool(layer.flags & 16),
            'urg': bool(layer.flags & 32),
            'ece': bool(layer.flags & 64),
            'cwr': bool(layer.flags & 128)
        }
        
        return {
            'src_port': layer.sport,
            'dst_port': layer.dport,
            'sequence_number': layer.seq,
            'acknowledgment_number': layer.ack,
            'header_length': layer.dataofs * 4,
            'flags': flags,
            'flags_value': layer.flags,
            'window_size': layer.window,
            'checksum': layer.chksum,
            'urgent_pointer': layer.urgptr,
            'options': layer.options if hasattr(layer, 'options') else [],
            'payload_size': len(layer.payload) if layer.payload else 0
        }
    
    def _parse_udp(self, layer: UDP) -> Dict[str, Any]:
        """解析UDP层"""
        return {
            'src_port': layer.sport,
            'dst_port': layer.dport,
            'length': layer.len,
            'checksum': layer.chksum,
            'payload_size': len(layer.payload) if layer.payload else 0
        }
    
    def _parse_icmp(self, layer: ICMP) -> Dict[str, Any]:
        """解析ICMP层"""
        return {
            'type': layer.type,
            'type_name': self._get_icmp_type_name(layer.type),
            'code': layer.code,
            'checksum': layer.chksum,
            'id': getattr(layer, 'id', None),
            'sequence': getattr(layer, 'seq', None)
        }
    
    def _parse_arp(self, layer: ARP) -> Dict[str, Any]:
        """解析ARP层"""
        return {
            'hardware_type': layer.hwtype,
            'protocol_type': layer.ptype,
            'hardware_size': layer.hwlen,
            'protocol_size': layer.plen,
            'operation': layer.op,
            'operation_name': self._get_arp_operation_name(layer.op),
            'sender_hw_addr': layer.hwsrc,
            'sender_proto_addr': layer.psrc,
            'target_hw_addr': layer.hwdst,
            'target_proto_addr': layer.pdst
        }
    
    def _parse_dns(self, layer: DNS) -> Dict[str, Any]:
        """解析DNS层"""
        result = {
            'transaction_id': layer.id,
            'flags': layer.qr,
            'opcode': layer.opcode,
            'authoritative': bool(layer.aa),
            'truncated': bool(layer.tc),
            'recursion_desired': bool(layer.rd),
            'recursion_available': bool(layer.ra),
            'response_code': layer.rcode,
            'question_count': layer.qdcount,
            'answer_count': layer.ancount,
            'authority_count': layer.nscount,
            'additional_count': layer.arcount,
            'queries': [],
            'answers': []
        }
        
        # 解析查询记录
        if hasattr(layer, 'qd') and layer.qd:
            for query in layer.qd:
                result['queries'].append({
                    'name': query.qname.decode('utf-8', errors='ignore'),
                    'type': query.qtype,
                    'class': query.qclass
                })
        
        # 解析应答记录
        if hasattr(layer, 'an') and layer.an:
            for answer in layer.an:
                result['answers'].append({
                    'name': answer.rrname.decode('utf-8', errors='ignore'),
                    'type': answer.type,
                    'class': answer.rclass,
                    'ttl': answer.ttl,
                    'data': str(answer.rdata)
                })
        
        return result
    
    def _parse_http(self, layer) -> Dict[str, Any]:
        """解析HTTP层"""
        result = {}
        
        if hasattr(layer, 'Method'):  # HTTP请求
            result.update({
                'type': 'request',
                'method': layer.Method.decode('utf-8', errors='ignore'),
                'uri': layer.Path.decode('utf-8', errors='ignore'),
                'version': layer.Http_Version.decode('utf-8', errors='ignore'),
                'headers': {}
            })
        elif hasattr(layer, 'Status_Code'):  # HTTP响应
            result.update({
                'type': 'response',
                'status_code': layer.Status_Code.decode('utf-8', errors='ignore'),
                'reason_phrase': layer.Reason_Phrase.decode('utf-8', errors='ignore'),
                'version': layer.Http_Version.decode('utf-8', errors='ignore'),
                'headers': {}
            })
        
        # 解析HTTP头部
        if hasattr(layer, 'fields'):
            for field_name, field_value in layer.fields.items():
                if isinstance(field_value, bytes):
                    result['headers'][field_name] = field_value.decode('utf-8', errors='ignore')
                else:
                    result['headers'][field_name] = str(field_value)
        
        return result
    
    def _extract_key_info(self, result: Dict[str, Any]) -> None:
        """提取关键信息到顶层"""
        # 提取源和目标信息
        if 'ip' in result['layers']:
            ip_layer = result['layers']['ip']
            result['src_ip'] = ip_layer['src_ip']
            result['dst_ip'] = ip_layer['dst_ip']
        elif 'ipv6' in result['layers']:
            ipv6_layer = result['layers']['ipv6']
            result['src_ip'] = ipv6_layer['src_ip']
            result['dst_ip'] = ipv6_layer['dst_ip']
        
        # 提取端口信息
        if 'tcp' in result['layers']:
            tcp_layer = result['layers']['tcp']
            result['src_port'] = tcp_layer['src_port']
            result['dst_port'] = tcp_layer['dst_port']
            result['protocol'] = 'TCP'
        elif 'udp' in result['layers']:
            udp_layer = result['layers']['udp']
            result['src_port'] = udp_layer['src_port']
            result['dst_port'] = udp_layer['dst_port']
            result['protocol'] = 'UDP'
        elif 'icmp' in result['layers']:
            result['protocol'] = 'ICMP'
        
        # 提取应用层协议
        if 'http' in result['layers']:
            result['application_protocol'] = 'HTTP'
        elif 'dns' in result['layers']:
            result['application_protocol'] = 'DNS'
    
    @staticmethod
    def _get_ether_type_name(ether_type: int) -> str:
        """获取以太网类型名称"""
        types = {
            0x0800: 'IPv4',
            0x0806: 'ARP',
            0x86DD: 'IPv6',
            0x8100: 'VLAN'
        }
        return types.get(ether_type, f'Unknown(0x{ether_type:04x})')
    
    @staticmethod
    def _get_ip_protocol_name(protocol: int) -> str:
        """获取IP协议名称"""
        protocols = {
            1: 'ICMP',
            6: 'TCP',
            17: 'UDP',
            41: 'IPv6',
            47: 'GRE',
            50: 'ESP',
            51: 'AH'
        }
        return protocols.get(protocol, f'Unknown({protocol})')
    
    @staticmethod
    def _get_icmp_type_name(icmp_type: int) -> str:
        """获取ICMP类型名称"""
        types = {
            0: 'Echo Reply',
            3: 'Destination Unreachable',
            8: 'Echo Request',
            11: 'Time Exceeded',
            12: 'Parameter Problem'
        }
        return types.get(icmp_type, f'Unknown({icmp_type})')
    
    @staticmethod
    def _get_arp_operation_name(operation: int) -> str:
        """获取ARP操作名称"""
        operations = {
            1: 'Request',
            2: 'Reply'
        }
        return operations.get(operation, f'Unknown({operation})')


# 全局协议解析器实例
protocol_parser = ProtocolParser()
