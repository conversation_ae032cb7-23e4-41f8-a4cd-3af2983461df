<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}网络流量分析与安全检测系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            margin-left: 0;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
        .status-card {
            border-left: 4px solid #007bff;
        }
        .status-card.warning {
            border-left-color: #ffc107;
        }
        .status-card.danger {
            border-left-color: #dc3545;
        }
        .status-card.success {
            border-left-color: #28a745;
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar position-fixed d-none d-md-block">
        <div class="p-3">
            <h5 class="text-white">流量分析系统</h5>
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link" href="#dashboard" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt me-2"></i>仪表板
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#traffic" onclick="showSection('traffic')">
                        <i class="fas fa-network-wired me-2"></i>流量监控
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#security" onclick="showSection('security')">
                        <i class="fas fa-shield-alt me-2"></i>安全事件
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#rules" onclick="showSection('rules')">
                        <i class="fas fa-cogs me-2"></i>检测规则
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#files" onclick="showSection('files')">
                        <i class="fas fa-file-archive me-2"></i>PCAP文件
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#settings" onclick="showSection('settings')">
                        <i class="fas fa-cog me-2"></i>系统设置
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- 主内容区域 -->
    <div class="main-content">
        <!-- 顶部导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light border-bottom">
            <div class="container-fluid">
                <button class="btn btn-outline-secondary d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#sidebar">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="navbar-brand mb-0 h1">网络流量分析与安全检测系统</span>
                <div class="navbar-nav ms-auto">
                    <div class="nav-item">
                        <span class="nav-link">
                            <i class="fas fa-circle text-success me-1"></i>
                            <span id="system-status">系统运行中</span>
                        </span>
                    </div>
                </div>
            </div>
        </nav>

        <!-- 页面内容 -->
        <div class="container-fluid p-4">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 全局变量
        let currentSection = 'dashboard';
        let refreshInterval;
        
        // 显示指定部分
        function showSection(section) {
            // 隐藏所有部分
            document.querySelectorAll('.content-section').forEach(el => {
                el.style.display = 'none';
            });
            
            // 显示指定部分
            const targetSection = document.getElementById(section + '-section');
            if (targetSection) {
                targetSection.style.display = 'block';
            }
            
            // 更新导航状态
            document.querySelectorAll('.sidebar .nav-link').forEach(el => {
                el.classList.remove('active');
            });
            document.querySelector(`[href="#${section}"]`).classList.add('active');
            
            currentSection = section;
            
            // 加载对应数据
            loadSectionData(section);
        }
        
        // 加载部分数据
        function loadSectionData(section) {
            switch(section) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'traffic':
                    loadTrafficData();
                    break;
                case 'security':
                    loadSecurityEvents();
                    break;
                case 'rules':
                    loadRules();
                    break;
                case 'files':
                    loadPcapFiles();
                    break;
                case 'settings':
                    loadSettings();
                    break;
            }
        }
        
        // API调用辅助函数
        async function apiCall(endpoint) {
            try {
                const response = await fetch(`/api/${endpoint}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error('API调用失败:', error);
                showAlert('API调用失败: ' + error.message, 'danger');
                return null;
            }
        }
        
        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            const container = document.querySelector('.container-fluid');
            container.insertBefore(alertDiv, container.firstChild);
            
            // 自动消失
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // 格式化时间
        function formatTime(isoString) {
            return new Date(isoString).toLocaleString('zh-CN');
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            const sizes = ['B', 'KB', 'MB', 'GB'];
            if (bytes === 0) return '0 B';
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            showSection('dashboard');
            
            // 设置定时刷新
            refreshInterval = setInterval(() => {
                if (currentSection === 'dashboard') {
                    loadDashboard();
                }
            }, 30000); // 30秒刷新一次
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
